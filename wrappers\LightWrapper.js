const token = 'c11d27f3a1081c5c6549c0205f9fe1ce5576adf554500b32d55dc6659b93be76'

const LightWrapper = {
    async validateColor(color) {
        const response = await fetch(`https://api.lifx.com/v1/color?string=${color}`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
        const data = await response.json()
        return data
    },
    async getLights(lightId = 'all') {
        const response = await fetch(`https://api.lifx.com/v1/lights/${lightId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
        const data = await response.json()
        return data
    },
    async setState(state, lightId = 'all') {
        const body = {
            power: state?.power || 'on',
            color: state?.color || 'white',
            brightness: state?.brightness || 1,
            duration: state?.duration || 1,
            // infrared: state?.infrared || ,
            fast: state?.fast || false,
            // hue: attributes.hue,
            // saturation: attributes.saturation,
            // kelvin: attributes.kelvin,
        }
        const response = await fetch(`https://api.lifx.com/v1/lights/${lightId}/state`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        })
        const data = await response.json()
        return data
    },
    async setStateDelta(state, lightId = 'all') {
        const body = {
            power: state?.power || 'on',
            duration: state?.duration || 1,
            hue: state?.hue || 0,
            saturation: state?.saturation || 1,
            brightness: state?.brightness || 1,
            kelvin: state?.kelvin || 2500,
            fast: state?.fast || false
        }
        const response = await fetch(`https://api.lifx.com/v1/lights/${lightId}/state/delta`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        })
        const data = await response.json()
        return data
    },
    async togglePower(lightId = 'all', duration = 1) {
        const body = {
            duration: duration
        }
        const response = await fetch(`https://api.lifx.com/v1/lights/${lightId}/toggle`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(body)
        })
        const data = await response.json()
        return data
    },
    async randomizeColor(lightId = 'all', duration = 1) {
        // const colors = [
        //     'purple', 'red', 'orange', 'yellow', 'green', 'blue', 'indigo'
        // ];

        // colors as hex values
        const colors = [
            '#800080',
            '#FF0000',
            '#FFA500',
            '#FFFF00',
            '#008000',
            '#0000FF',
            '#4B0082',
            '#FF00FF',  // Magenta/Hot Pink - very vibrant
            '#00FFFF',  // Cyan - opposite of red/pink
            '#FF0000',  // Pure Red - strong primary
            '#00FF00',  // Lime/Neon Green - electric bright
            '#0000FF',  // Pure Blue - cool contrast
            '#FFFF00',  // Yellow - maximum brightness
            '#FF4500',  // Orange Red - intense warm
            '#9400D3',  // Violet - deep purple
            '#00CED1',  // Dark Turquoise - unique blue-green
            '#FF1493',  // Deep Pink - vibrant pink
            '#32CD32',  // Lime Green - different from neon
            '#FF8C00',  // Dark Orange - rich warm
        ];
        
        // Initialize the current color if it doesn't exist
        if (!this.currentColor) {
            this.currentColor = colors[Math.floor(Math.random() * colors.length)];
        }

        try {
            // Filter out the current color to avoid picking the same one
            const availableColors = colors.filter(color => color !== this.currentColor);
            
            // Pick a random color from the available colors
            const randomIndex = Math.floor(Math.random() * availableColors.length);
            const newColor = availableColors[randomIndex];
            
            // Set the new color
            await this.setState({ color: newColor, duration: duration }, lightId);
            
            // Update the current color
            this.currentColor = newColor;
        } catch (error) {
            console.error('Error randomizing color:', error);
        }
    },
    async cycleColor(lightId = 'all', duration = 1) {
        const colors = [
            '#800080',
            '#FF0000',
            '#FFA500',
            '#FFFF00',
            '#008000',
            '#0000FF',
            '#4B0082',
            '#FF00FF',  // Magenta/Hot Pink - very vibrant
            '#00FFFF',  // Cyan - opposite of red/pink
            '#FF0000',  // Pure Red - strong primary
            '#00FF00',  // Lime/Neon Green - electric bright
            '#0000FF',  // Pure Blue - cool contrast
            '#FFFF00',  // Yellow - maximum brightness
            '#FF4500',  // Orange Red - intense warm
            '#9400D3',  // Violet - deep purple
            '#00CED1',  // Dark Turquoise - unique blue-green
            '#FF1493',  // Deep Pink - vibrant pink
            '#32CD32',  // Lime Green - different from neon
            '#FF8C00',  // Dark Orange - rich warm
        ];
        
        // Initialize the index as a static property if it doesn't exist
        if (!this.colorIndex) {
            this.colorIndex = 0;
        }

        try {
            // Flash white quickly before changing to the next color
            // await this.setState({ color: 'white', duration: 0.1 }, lightId);
            // await new Promise(resolve => setTimeout(resolve, 100)); // Wait 100ms
            
            const color = colors[this.colorIndex];
            await this.setState({ color: color, duration: duration }, lightId);
            
            // Move to next color, cycling back to 0 after last color
            this.colorIndex = (this.colorIndex + 1) % colors.length;
        } catch (error) {
            console.error('Error cycling color:', error);
        }
    }
};

module.exports = LightWrapper;
