const MessageConstant = require('../constants/MessageConstants.js');
const ClockCrewConstants = require('../constants/ClockCrewConstants.js');
const { BACKSTORY_MESSAGE, PERSONALITY_MESSAGE, ASSISTANT_MESSAGE } = require('../config.json');

const MessageService = {
    assembleAssistantMessage(canGenerateImage, guildId) {
        let message = '';
        if (ASSISTANT_MESSAGE) {
            message = ASSISTANT_MESSAGE;
        } else {
            if (canGenerateImage) {
                message += `# Generative capabilities`;
                message += `\n- You are able to generate text.`;
                message += `\n- You are able to generate images such as paintings, drawings, and more.`;
                message += `\n- You are able to draw, paint, sketch, design, illustrate, and create images.`;
                message += `\n- If you were previously unable to generate images, you are now able to do so.`;
                message += `\n- You cannot generate sound or audio.`;
                message += `\n\n`;
            } else {
                message += `\n\n# Generative capabilities`;
                message += `\n- You are able to generate text.`;
                message += `\n- You cannot generate images, paintings, or drawings.`;
                message += `\n- You cannot draw, paint, sketch, design, illustrate, or create images.`;
                message += `\n- If you were previously able to generate images, you are no longer able to do so.`;
                message += `\n- You cannot generate sound or audio.`;
                message += `\n- When asked to draw or generate an image, tell the person to ask Rodrigo to turn on the image generation capabilities.`;
                message += `\n\n`;
            }
            if (guildId === '249010731910037507') {
                const clockWithoutProfiles = ClockCrewConstants.clocks_without_profiles;
                const clocksWithProfiles = ClockCrewConstants.clocks_with_profiles;
                // merge arrays
                const allClocks = [...clockWithoutProfiles, ...clocksWithProfiles];

                if (allClocks.length) {
                    message += `\n# List of Clocks`;
                    for (const clock of allClocks) {
                        const url = clock.url;
                        const name = clock.name;
                        const description = clock.description;
                        message += `\n- ${name}`
                        if (url) {
                            message += `\n  - Newgrounds URL/Profile/Account: ${url}`;
                            if (description) {
                                message += `\n  - Description: ${description}`;
                            }
                        }
                    }
                }


                message += `${MessageConstant.clockcrewAssistantMessage}`;
            } else {
                message += `${MessageConstant.assistantMessage}`;
            }
        }
        return message
    },
    assembleBackstoryMessage(guildId) {
        if (guildId) {
            return BACKSTORY_MESSAGE ? BACKSTORY_MESSAGE : MessageConstant.backstoryMessage
        }
    },
    assemblePersonalityMessage() {
        return PERSONALITY_MESSAGE ? PERSONALITY_MESSAGE : MessageConstant.personalityMessage
    },
};

module.exports = MessageService;
