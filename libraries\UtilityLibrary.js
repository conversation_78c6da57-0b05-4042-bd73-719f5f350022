const moment = require('moment');
const luxon = require('luxon');
const config = require('../config.json');
const crypto = require('crypto');

const colors = [
    'black',
    'red',
    'green',
    'yellow',
    'blue',
    'magenta',
    'cyan',
    'white',
    'gray',
    'pink',
    'teal',
    'canary',
    'azure',
    'fuchsia',
    'aqua',
    'snow',
]

function removeFlaggedWords(text) {
    let modifiedText = text;
    const flaggedWordsArray = config.FLAGGED_WORDS.split(", "); 

    flaggedWordsArray.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, "gi");
        modifiedText = modifiedText.replace(regex, (match) => match.replace(/./g, '|| || '));
        
    });
    
    return modifiedText;
}

function removeMentions(text) {
    return text
    .replace(/@here/g, '꩜here')
    .replace(/@everyone/g, '꩜everyone')
    .replace(/@horde/g, '꩜horde')
    .replace(/@alliance/g, '꩜alliance')
    .replace(/@alliance/g, '꩜alliance')
    .replace(/@Guild Leader - Horde/g, '꩜Guild Leader - Horde')
    .replace(/@Guild Leader - Alliance/g, '꩜Guild Leader - Alliance')
    .replace(/@Guild Officer - Horde/g, '꩜Guild Officer - Horde')
    .replace(/@Guild Officer - Alliance/g, '꩜Guild Officer - Alliance')
}

const UtilityLibrary = {
    async generateFileHash(url) {
        try {
            const response = await fetch(url);
            const bytes = await response.bytes();
            const buffer = Buffer.from(bytes);
            const fileType = response.headers.get('content-type');
            
            const hash = crypto.createHash('sha256').update(buffer).digest('hex');
            return { hash, fileType };
        } catch (error) {
            console.log(`❌ [UtilityLibrary:generateFileHash] Error generating hash:\n`, `${error}`);
            throw error;
        }
    },
    removeFlaggedWords: removeFlaggedWords,
    removeMentions: removeMentions,
    async isImageUrl(url) {
        try {
            const response = await fetch(url);
            const contentType = response.headers.get('content-type');
            return contentType.startsWith('image/');
        } catch (error) {
            console.error(`❌ [UtilityLibrary:isImageUrl] Error checking if URL is an image:\n`, `${error}`);
            return false;
        }
    },
    // Date Utilities
    getCurrentDateAndTime(date) {
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    },
    getMinutesAgo(date) {
        return moment(date).fromNow();  
    },
    consoleInfo(symbol, functionName, message) {
        if (symbol && functionName && message) {
            const time = luxon.DateTime.now().toFormat('h:mm:ss a');
            console.info(`${time} - ${symbol}${functionName}: ${message}`);
        } else if (symbol && functionName) {
            const time = luxon.DateTime.now().toFormat('h:mm:ss a');
            console.info(`${time} - ${symbol}${functionName}`);
        }
    },
    consoleLog(symbol, message, styleOptions = {}) {
        const debugLevel = 3;
        if (!symbol) {
            return;
        }
        const resetStyle = "\x1b[0m";
        
        const stack = new Error().stack;
        // console.log(stack);
        const callerLine = stack.split('\n')[2];
        let trimmedCallerLine = callerLine.trim().replace('at ', '');

        trimmedCallerLine = trimmedCallerLine.replace('as _', '_').replace('[', '').replace(']', '').replace('(', '').replace(')', '');
        const splitString = trimmedCallerLine.split(' ');
        // console.log(splitString);
        let funcName;
        let lineLocation;
        if (splitString.length === 3) {
            funcName = splitString[0];
            lineLocation = splitString[2];
        } else {
            funcName = splitString[0];
            lineLocation = splitString[1];
        }

        
        // finalOutput += `\n\x1b[3m\x1b[37m${funcName} ${lineLocation}\x1b[0m`;
    
        // --- Constants for styling ---
        const colorCodes = {
            'black': 30, 'red': 31, 'green': 32, 'yellow': 33,
            'blue': 34, 'magenta': 35, 'cyan': 36, 'white': 37,
            'orange': 33,
        };
    
        const time = luxon.DateTime.now().toFormat('h:mm:ss a');
    

        let logText = '';

        const location = `\n${resetStyle}\x1b[2m\x1b[3m\x1b[37m(${lineLocation})${resetStyle}`


        if (debugLevel >= 2) {
            if (symbol === '<') {
                logText = `${symbol}${funcName}`;
            } else if (symbol === '>' || symbol === '=') {
                logText = `${symbol}${funcName}`;
            }
        }

        if (message !== undefined && message !== null) {
            logText += `\n${message}`;
        }

        if (debugLevel >= 3) {
            if (symbol === '<') {
                logText += location;
            }
        }
    
        const {
            bold = false,
            faint = false,
            italic = false,
            underline = false,
            slowBlink = false,
            rapidBlink = false,
            crossedOut = false,
            doubleUnderline = false,
            superscript = false, // Note: Support varies widely across terminals
            subscript = false,   // Note: Support varies widely across terminals
            color = null      // Default to no color
        } = styleOptions;
    
        const styleCodeList = [
            bold ? '1' : '',
            faint ? '2' : '',
            italic ? '3' : '',
            underline ? '4' : '',
            slowBlink ? '5' : '',
            rapidBlink ? '6' : '',
            crossedOut ? '9' : '',
            doubleUnderline ? '21' : '',
            superscript ? '73' : '',
            subscript ? '74' : '',
        ].filter(code => code); // Remove empty strings
    
        // Add color code if specified and valid
        const lowerCaseColor = color ? String(color).toLowerCase() : null;
        if (lowerCaseColor && colorCodes[lowerCaseColor]) {
            styleCodeList.push(colorCodes[lowerCaseColor]);
        }

        if (symbol === '<') {
            styleCodeList.push('1');
            styleCodeList.push('34');
        } else if (symbol === '>') {
            styleCodeList.push('1');
            styleCodeList.push('32');
        } else if (symbol === '>!') {
            styleCodeList.push('1');
            styleCodeList.push('31');
        } else if (symbol === '=') {
            styleCodeList.push('33');
        }

        if (logText.length) {
            let finalOutput = '';
            finalOutput = `${time} - `;
            if (styleCodeList.length > 0) {
                const stylePrefix = `\x1b[${styleCodeList.join(';')}m`;
                finalOutput += `${stylePrefix}${logText}${resetStyle}`;
            } else {
                // No styles applied
                finalOutput += logText;
            }
    
            if (debugLevel === 3) {
                if (symbol === '>' || symbol === '=') {
                    finalOutput += ` ${location}`;
                }
            }
    
            console.info(finalOutput);
        } 
    },
    consoleInfoColor(messages) {
        const colorCodes = ['black', 'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white', 'orange']; // Define more as needed
        const resetStyle = "\x1b[0m";

        // print current time as 1:23:45PM
        const time = luxon.DateTime.now().toFormat('h:mm:ss a');
        
        const formattedMessages = messages.map(([message, { bold, faint, italic, underline, slowBlink, rapidBlink, crossedOut, doubleUnderline, superscript, subscript, color }, position = {}]) => {
            const colorIndex = color && colorCodes.includes(color.toLowerCase()) ? colorCodes.indexOf(color.toLowerCase()) + 30 : '';
            const colorCode = color ? `;${colorIndex}` : '';
            const styleCodes = [
                bold ? '1' : '',
                faint ? '2' : '',
                italic ? '3' : '',
                underline ? '4' : '',
                slowBlink ? '5' : '',
                rapidBlink ? '6' : '',
                crossedOut ? '9' : '',
                doubleUnderline ? '21' : '',
                superscript ? '73' : '',
                subscript ? '74' : '',
            ].filter(code => code).join(';');
            const style = `\x1b[${styleCodes}${colorCode}m`;

            // position can be 'start', 'middle', 'end'
            // for start return ╔
            // for middle return ║
            // for end return ╚
            const positionCodes = {
                start: '╔',
                middle: '║',
                end: '╚',
            };
    
            if (typeof message === 'object') {
                return [style, message, resetStyle];
            } else {
                return `${time} - ${positionCodes[position] ? positionCodes[position] : ''}${style}${message}${resetStyle}`;
            }
        });
    
        console.info(...formattedMessages.flat());
    },
    capitalize(string) {
        if (string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }
    },
    getUsernameNoSpaces(message) {
        let name = message?.author?.displayName || message?.author?.username || message?.user?.username;
        let username = 'default';
        if (name) {
            username = name ? name.replace(/\s+/g, '_').replace(/[^\w\s]/gi, '') : message?.author?.username || message?.user?.username;
            if (!username) {
                username = message?.author?.username || message?.user?.username || 'default';
            }
        }
        return username;
    },
    howl() {
        let howl = 'Aw';
        let randomize = Math.floor(Math.random() * 10) + 1;
        for (let i = 0; i < randomize; i++) {
            howl = howl + 'o';
        }
        howl = howl + '!';
        return howl;
    },
    areArraysEqual(array1, array2) {
        return array1.length === array2.length &&
        array1.every(item1 =>
            array2.some(item2 =>
            Object.keys(item1).length === Object.keys(item2).length &&
            Object.entries(item1).every(([key, val]) => item2.hasOwnProperty(key) && item2[key] === val)
            )
        ) &&
        array2.every(item1 =>
            array1.some(item2 =>
            Object.keys(item1).length === Object.keys(item2).length &&
            Object.entries(item1).every(([key, val]) => item2.hasOwnProperty(key) && item2[key] === val)
            )
        );
    },
    // Discord Utilities
    findUserById(client, userId) {
        const user = client.users.cache.get(userId)
        return UtilityLibrary.discordUsername(user)
    },
    discordBotsAmount(message) {
        if (message) {
            const bots = message.guild.members.cache.filter(member => member.user.bot).size;
            return bots;
        }
    },
    discordRoles(member) {
        if (member) {
            let roles = member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.name).join(', ');
            return roles;
        }
    },
    discordUsername(authorOrUser) {
        if (authorOrUser) {
            const username = authorOrUser?.displayName || authorOrUser?.username || authorOrUser?.globalName;
            return username;
        }
    },
    discordUserId(message) {
        if (message) {
            const userId = message?.author?.id || message?.user?.id;
            return userId;
        }
    },
    discordUserMention(message) {
        if (message) {
            const userId = message?.author?.id || message?.user?.id;
            return `<@${userId}>`;
        }
    },
    discordUserTag(item) {
        if (item) {
            const userTag = item?.author?.tag || item?.user?.tag;
            return userTag;
        }
    },
    discordGetMember(message) {
        if (message) {
            const userId = message?.author?.id || message?.user?.id;
            const member = message.guild.members.cache.get(userId);
            return member;
        }
    },
    detectHowlAndRespond(message) {
        if (message.content.toLowerCase().includes('!howl')) {
            const howl = UtilityLibrary.howl(message);
            message.channel.send(howl);
        }
    },
    async detectMessageAndReact(message) {
        if (message.author.id !== message.client.user.id && (message.content.toLowerCase().includes('lupos') || message.content.toLowerCase().includes('good dog') || message.content.toLowerCase().includes('good boy'))) {
            try {
                await message.react('1194383720946352200');
            } catch (error) {
                // console.error('One of the emojis failed to react.');
                // Usually because someone has blocked the bot, so the bot cannot react.
            }
        }
    },
    ansiEscapeCodes(isConsoleLog = false) {
        const bold = (text) => isConsoleLog ? `\x1b[1m${text}\x1b[0m` : text;
        const faint = (text) => isConsoleLog ? `\x1b[2m${text}\x1b[0m` : text;
        const italic = (text) => isConsoleLog ? `\x1b[3m${text}\x1b[0m` : text;
        const underline = (text) => isConsoleLog ? `\x1b[4m${text}\x1b[0m` : text;
        const slowBlink = (text) => isConsoleLog ? `\x1b[5m${text}\x1b[0m` : text;
        const rapidBlink = (text) => isConsoleLog ? `\x1b[6m${text}\x1b[0m` : text;
        const inverse = (text) => isConsoleLog ? `\x1b[7m${text}\x1b[0m` : text;
        const hidden = (text) => isConsoleLog ? `\x1b[8m${text}\x1b[0m` : text;
        const strikethrough = (text) => isConsoleLog ? `\x1b[9m${text}\x1b[0m` : text;
        return {
            bold,
            faint,
            italic,
            underline,
            slowBlink,
            rapidBlink,
            inverse,
            hidden,
            strikethrough
        };
    },
};

module.exports = UtilityLibrary;
