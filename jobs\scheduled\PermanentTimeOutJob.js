const DiscordUtilityService = require('../../services/DiscordUtilityService.js');
const config = require('../../config.json');
const { consoleLog } = require('../../libraries/UtilityLibrary.js');
const timedOutUsers = require('../../arrays/timedOutUsers.js');

const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
const oneWeek = 7 * 24 * 60 * 60 * 1000; // 1 week in milliseconds
const oneSecond = 1000; // 1 second in milliseconds

const LogFormatter = require('../../formatters/LogFormatter.js');

async function timeOutUsers(client, mongo) {
    const guild = DiscordUtilityService.getGuildById(client, config.GUILD_ID_WHITEMANE);
    for (const userId of timedOutUsers) {
        let member;
        try {
            member = await guild.members.fetch(userId);
            if (member) {
                // const oneMinute = 60 * 1000; // 1 minute in milliseconds
                const totalTime = oneWeek + oneSecond; // 5 minutes + 1 second
                const duration = totalTime / 1000; // Convert to seconds
                // const twentyDays = 20 * 24 * 60 * 60 * 1000; // 20 days in milliseconds
                // const randomTime = Math.floor(Math.random() * (twentyDays - oneMinute) + oneMinute);
                console.log(LogFormatter.timeOutMember(member, guild, duration));
            }
        } catch (err) {
            // console.error(`❌ [PermanentTimeOutJob:timeOutUsers] Failed to timeout user with ID ${userId}: ${err.message}`);
        }
        if (!member) {
            const user = await DiscordUtilityService.fetchUserFromClientAndUserId(client, userId);
            if (user) {
                console.warn(LogFormatter.timeOutMemberNotFound(user, guild));
            } else {
                console.warn(LogFormatter.timeOutUserDoesntExist(userId));
            }
        }
    }
}

const PermanentTimeOutJob = {
    async startJob(client, mongo) {
        await timeOutUsers(client, mongo); // Execute immediately
        setInterval(() => {
            timeOutUsers(client, mongo);
        }, oneWeek); // every five minutes
    }
};

module.exports = PermanentTimeOutJob;