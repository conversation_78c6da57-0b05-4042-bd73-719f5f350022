// Packages
const luxon = require('luxon');
const config = require('../config.json');
// Constants
const MessageConstant = require('../constants/MessageConstants.js');
// Formatters
const LogFormatter = require('../formatters/LogFormatter.js');
// Wrappers
const ComfyUIWrapper = require('../wrappers/ComfyUIWrapper.js');
const OpenAIWrapper = require('../wrappers/OpenAIWrapper.js');
const LocalAIWrapper = require('../wrappers/LocalAIWrapper.js');
const AnthrophicWrapper = require('../wrappers/AnthropicWrapper.js');
const PuppeteerWrapper = require('../wrappers/PuppeteerWrapper.js');
// Libraries
const UtilityLibrary = require('../libraries/UtilityLibrary.js');
const { consoleLog } = require('../libraries/UtilityLibrary.js');
const DiscordUtilityService = require('./DiscordUtilityService.js');
// Services

// File System
const fs = require('fs');
const path = require('path');
const { duration } = require('moment/moment.js');

async function generateStickerResponse(message, localMongo) {
    // if sticker
    let content = '';
    if (message.stickers.size === 1) {
        const sticker = message.stickers.first();
        const url = sticker.url;
        const imageCaptions = await captionImages([url], message.id, localMongo);
        const imageCaption = imageCaptions[0];
        content += `\nType: Sticker Message`;
        content += `\nSticker Name: ${sticker.name}`;
        if (sticker.description) {
            content += `\nSticker Description: ${sticker.description}`;
        }
        if (imageCaption) {
            content += `\nSticker Caption: ${imageCaption}`;
        }
    }
    return content;
}

async function generateEmojiResponse(message, isReply=false) {
    // if emojis
    let who = '';
    if (isReply) {
        // who = `Original `;
    }
    let content = '';
    if (message.reactions.cache.size > 0) {
        const repliedReactions = message.reactions.cache.map((reaction) => {
            return `${reaction.emoji.name}`;
        });
        content += `\n${who}Reactions (${message.reactions.cache.size}):`;
        content += `\n  • ${repliedReactions.join(', ')}`;
    }
    return content;
}

async function generateAttachmentsResponse(message, imagesCollection, isReply=false) {
    // if attachments
    let who = '';
    if (isReply) {
        // who = `Original `;
    }
    let content = '';
    if (message?.attachments?.size > 0) {
        content += `\n${who}Attachments:`;
        if (imagesCollection?.size > 0) {
            for (const [key, image] of imagesCollection) {
                content += `\n  • Image ${key}: ${image.imageDescription}`;
            }
        }
    }
    return content;
}

async function transcribeAudio(audioUrls, messageId, localMongo) {
    let audios;
    const db = localMongo.db('lupos');
    const collection = db.collection('audioDescriptions');
    if (audioUrls?.length) {
        // Parse the URL to get just the filename without query parameters
        const url = new URL(audioUrls[0]);
        const filename = path.basename(url.pathname);
        const { hash, fileType } = await UtilityLibrary.generateFileHash(audioUrls[0]);
        const existingAudio = await collection.findOne({ hash });

        if (!existingAudio) {
            // Create the voices directory if it doesn't exist
            const voicesDir = path.join(__dirname, '../voices');
            if (!fs.existsSync(voicesDir)) {
                fs.mkdirSync(voicesDir, { recursive: true });
            }
            
            // Download the audio file
            const audioFilePath = path.join(voicesDir, `${messageId}-${filename}`);
            const audioFile = await fetch(audioUrls[0]);
            const audioBuffer = await audioFile.arrayBuffer();
            fs.writeFileSync(audioFilePath, Buffer.from(audioBuffer));

            // Create a read stream for the transcription
            audios = await OpenAIWrapper.speechToText(fs.createReadStream(audioFilePath));
            // remove any extra \n or whitespace
            audios = audios.trim().replace(/\n+/g, ' ');
            await collection.insertOne({
                hash,
                url: audioUrls[0],
                description: audios,
                type: fileType,
                createdAt: new Date(),
            });
        } else {
            audios = existingAudio.description;
        }
    }
    return audios;
}

async function captionImages(imageUrls, messageId, localMongo) {
    let images = [];
    const db = localMongo.db('lupos');
    const collection = db.collection('imageDescriptions');
    if (imageUrls?.length) {
        for (const imageUrl of imageUrls) {
            const { hash, fileType } = await UtilityLibrary.generateFileHash(imageUrl);
            const existingImage = await collection.findOne({ hash });
            if (!existingImage) {
                const { response } = await AIService.generateVision(imageUrl, 'Describe this image');
                if (response?.choices[0]?.message?.content) {
                    const description = response.choices[0].message.content;
                    images.push(description);
                    await collection.insertOne({
                        hash,
                        url: imageUrl,
                        description,
                        fileType,
                        createdAt: new Date(),
                    });
                }
            } else {
                images.push(existingImage.description);
            }
        }
    }
    return images;
}

async function generateIsDecliningToAnswer(generatedImagePrompt, message) {
    consoleLog('<');
    let conversation = [
        {
            role: 'system',
            content: `You are an expert at detecting if a message is declining to answer a question or unable to provide an answer. You will answer with a yes if the message is declining to answer. You will answer with a no if the message is not declining to answer. You will only output a yes or no, nothing else.
            
            Here is an example of a message that is declining to answer:
            "I will not provide that type of description or engage with that content. However, I'd be happy to have a respectful conversation about food, gardening, or other topics that don't involve harmful language or inappropriate themes."`
        },
        {
            role: 'user',
            name: UtilityLibrary.getUsernameNoSpaces(message),
            content: generatedImagePrompt,
        }
    ]
    
    let response = await AIService.generateText({ conversation, type: 'OPENAI', performance: 'FAST', tokens: 3 });
    consoleLog('=', `RESPONSE:\n\n${response}`);
    consoleLog('>');
    return response;
}

function assembleConversation(systemMessage, userMessage, message) {
    let conversation = [
        {
            role: 'system',
            content: systemMessage
        },
        {
            role: 'user',
            name: UtilityLibrary.getUsernameNoSpaces(message) || 'Default',
            content: userMessage,
        }
    ]
    return conversation;
}

// async function generateTextFromSystemUserMessages(systemMessage, userMessage, message) {
//     const conversation = assembleConversation(systemMessage, userMessage, message);
//     const type = 'OPENAI';
//     const performance = 'FAST';
//     const tokens = 600;
//     const generateTextOptions = { conversation: conversation, type: type, performance: performance, tokens: tokens };
//     return await AIService.generateText(generateTextOptions);
// }

const AIService = {
    async generateText({
        conversation,
        type=config.LANGUAGE_MODEL_TYPE,
        performance=config.LANGUAGE_MODEL_PERFORMANCE,
        temperature=config.LANGUAGE_MODEL_TEMPERATURE,
        tokens=config.LANGUAGE_MODEL_MAX_TOKENS
    }) {
        let textResponse;
        let generateTextModel;
        let inputTokenCount = 0;
        let currentTime = new Date().getTime();
        if (type === 'OPENAI') {
            if (performance === 'LOW') {
                generateTextModel = config.LANGUAGE_MODEL_OPENAI_LOW;
                textResponse = await OpenAIWrapper.generateOpenAITextResponse(conversation, generateTextModel, tokens, temperature);
            } else {
                generateTextModel = performance === 'FAST' ? config.FAST_LANGUAGE_MODEL_OPENAI : config.LANGUAGE_MODEL_OPENAI;
                textResponse = await OpenAIWrapper.generateOpenAITextResponse(conversation, generateTextModel, tokens, temperature);
            }
        } else if (type === 'ANTHROPIC') {
            generateTextModel = performance === 'FAST' ? config.ANTHROPIC_LANGUAGE_MODEL_FAST : config.ANTHROPIC_LANGUAGE_MODEL_SMART;
            if (conversation[conversation.length - 1].content === "") {
            conversation[conversation.length - 1].content = "hey";
            }
            textResponse = await AnthrophicWrapper.generateAnthropicTextResponse(conversation, generateTextModel, tokens, temperature);
            // inputTokenCount = await AnthrophicWrapper.countTokens(conversation, generateTextModel);

        } else if (type === 'LOCAL') {
            generateTextModel = performance === 'FAST' ? config.FAST_LANGUAGE_MODEL_LOCAL : config.LANGUAGE_MODEL_LOCAL;
            textResponse = await LocalAIWrapper.generateLocalAITextResponse(conversation, generateTextModel, tokens, temperature);
        } else if (type === 'GOOGLE') {
            // let systemInstruction;
            // if (conversation[0].role === 'system') {
            //     systemInstruction = conversation[0].content;
            // }
            // textResponse = await GoogleAIWrapper.generateChat(
            //     history=conversation,
            //     systemInstruction=systemInstruction,
            //     model='',
            //     maxTokens=tokens,
            //     temperature=temperature
            // );
        }

        // count characters in conversation
        let conversationLength = 0;
        for (let i = 0; i < conversation.length; i++) {
            conversationLength += conversation[i].content.length;
        }

        let timeTakenInSeconds = (new Date().getTime() - currentTime) / 1000;
        console.log(LogFormatter.generateFormatter({
            modelType: type,
            modelName: generateTextModel,
            duration: timeTakenInSeconds,
            characterCount: conversationLength,
            inputTokenCount: inputTokenCount,
        }));
        return textResponse;
    },
    async generateVision(imageUrl, text) {
        const { response, error } =  await OpenAIWrapper.generateVisionResponse(imageUrl, text);
        if (response) {
            console.log(`✅ [AIService:generateVision]
        Image URL: ${imageUrl}
        Prompt: ${text}
        Response: ${response.choices[0].message.content}`);
        } else if (error) {
            console.error(`❌ [AIService:generateVision]
        Image URL: ${imageUrl}
        Prompt: ${text}
        Error: ${error}`);
        }
        return { response, error };
    },
    async generateImage(prompt, type='FLUX') {
        consoleLog('<');
        let generatedImage;
        try {
            await ComfyUIWrapper.checkComfyUIWebsocketStatus();
            let currentTime = new Date().getTime();
            if (prompt) {
                consoleLog('=', `Type: ${type}`);
                consoleLog('=', `PROMPT:\n\n${prompt}`);
                generatedImage = await ComfyUIWrapper.generateComfyUIImage(prompt);
                let timeTakenInSeconds = (new Date().getTime() - currentTime) / 1000;
                consoleLog('=', `Time: ${timeTakenInSeconds}`);
                consoleLog('=', `RESPONSE:\n\n🖼️`);
                consoleLog('>');
            }
        } catch (error) {
            consoleLog('=', `RESPONSE:\n\n${error}`);
            consoleLog('>!');
        }
        return generatedImage;
    },
    async generateImageToImage(text, imageUrl, denoisingStrength) {
        consoleLog('<');
        consoleLog('=', `PROMPT:\n\n${text}`);
        let generatedImage;
        try {
            await ComfyUIWrapper.checkComfyUIWebsocketStatus();
            let currentTime = new Date().getTime();
            if (text) {
                generatedImage = await ComfyUIWrapper.generateComfyUIImageToImage(text, imageUrl, denoisingStrength);
                let timeTakenInSeconds = (new Date().getTime() - currentTime) / 1000;
                consoleLog('=', `Type: FLUX`);
                consoleLog('=', `Time: ${timeTakenInSeconds}`);
                consoleLog('=', `RESPONSE:\n\n🖼️`);
                consoleLog('>');
            }
        } catch (error) {
            consoleLog('=', `RESPONSE:\n\n${error}`);
            consoleLog('>!');
        }
        return generatedImage;
    },
    async generateSummaryFromMessage(message, messageContent) {
        let summary = '';
        const systemContent = `You are an expert at summarizing the text that is given to you in two to three words. Start with an emoji. Do not use any other formatting, just give the emoji and the two to three words.`;
        const conversation = assembleConversation(systemContent, messageContent, message);
        const generatedText = await AIService.generateText({    
            conversation: conversation,
            type: config.LANGUAGE_MODEL_TYPE,
            performance: 'POWERFUL',
            tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
            temperature: config.LANGUAGE_MODEL_TEMPERATURE
        });
        // trim generatedText to 128 characters
        summary = generatedText.substring(0, 128);
        return summary;
    },
    async generateCustomEmojiReactFromMessage(message) {
        const client = message.client;
        const guild = message.guild;
        const bot = client.user;
        const content = message.content;
        const modifiedMessageContent = content.replace(`<@${bot.id}>`, '');

        let guildEmojiList;
        let serverEmojisArray = [];

        if (guild) {    
            const serverEmojis = client.guilds.cache.get(guild.id).emojis.cache;
            serverEmojisArray = Array.from(serverEmojis.values());
            if (serverEmojisArray.length) {
                guildEmojiList = `# CUSTOM EMOJIS AVAILABLE:\n`;
                guildEmojiList += serverEmojisArray.map(emoji => emoji.name).join(', ');
                guildEmojiList += `\n\n`;
            }
        }
        
        const systemContent = `You are an expert at generating emoji reactions to text messages. 

    # INSTRUCTIONS:
    - Analyze the message and respond with a single, relevant emoji reaction
    - You can use either:
    1. A standard Unicode emoji (like 😂, ❤️, 👍, etc.)
    2. A custom server emoji name from the list below (return just the name, no colons or formatting)

    ${guildEmojiList}
    # RESPONSE FORMAT:
    - For Unicode emojis: Return just the emoji character
    - For custom emojis: Return just the emoji name (e.g., "pogchamp", "kekw")
    - Return ONLY the emoji or emoji name, nothing else
    - No explanations, no punctuation, no extra text`;
        
        const conversation = assembleConversation(systemContent, modifiedMessageContent, message);
        
        const generatedText = await AIService.generateText({    
            conversation: conversation,
            type: config.LANGUAGE_MODEL_TYPE,
            performance: 'POWERFUL',
            tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
            temperature: config.LANGUAGE_MODEL_TEMPERATURE
        });
        
        // Clean up the response - remove any extra whitespace, newlines, or formatting
        let cleanedResponse = generatedText.trim().replace(/[\n\r]/g, '');

        if (serverEmojisArray.length) {
            // check if its emoji or custom emoji
            const isCustomEmoji = serverEmojisArray.some(emoji => emoji.name === cleanedResponse);
            if (isCustomEmoji) {
                // <:blobreach:123456789012345678>
                // if its custom, wrap it in <:
                cleanedResponse = `${serverEmojisArray.find(emoji => emoji.name === cleanedResponse).id}`;
            }
        }

        
        return cleanedResponse;
    },
    async createImagePromptFromImageAndText(queuedDatum, imagePrompt, textResponse, mentionsCollection) {
        consoleLog('<');
        const { message} = queuedDatum;
        const randomText = [
            `Always include written text describes the theme of the image in quotes.`,
            `Always include text sign describes the theme of the the image in quotes.`,
            `Always include a speech bubble with text describes the theme of the the image in quotes.`,
        ]
        const pickRandomText = randomText[Math.floor(Math.random() * randomText.length)];
        let mentionedUsersNames = '';
        for (const mentionedUser of mentionsCollection.values()) {
            if (mentionedUser?.user?.username) {
                mentionedUsersNames += `${mentionedUser.user.username} `;
            }
        }
        let conversation = [
            {
                role: 'system',
                content: `${MessageConstant.newSystemContentImagePrompt}`
            },
            {
                role: 'user',
                name: UtilityLibrary.getUsernameNoSpaces(message),
                content: 
    `Combine these prompts into a single detailed image description:

    Image prompt: ${imagePrompt}

    Text prompt: ${textResponse}

    Include text element in quotes that references "${mentionedUsersNames || UtilityLibrary.getUsernameNoSpaces(message)}"

    ${pickRandomText}
    `
            }
        ]

        console.log('=', `CONVERSATION:\n\n${JSON.stringify(conversation, null, 2)}`);
        console.log('=', `PROMPT:\n\n${imagePrompt}`);
        console.log('=', `TEXT RESPONSE:\n\n${textResponse}`);

        if (config.DEBUG_MODE) {
            // UtilityLibrary.consoleInfoColor([[`🎨 Image prompt 1:\n${imagePrompt}`, { color: 'cyan' }, 'middle']]);
            // UtilityLibrary.consoleInfoColor([[`🎨 Image prompt 2:\n${textResponse}`, { color: 'blue' }, 'middle']]);
        }

        let generatedImagePrompt = await AIService.generateText({
            conversation: conversation,
            type: config.LANGUAGE_MODEL_TYPE,
            tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
            temperature: config.LANGUAGE_MODEL_TEMPERATURE
        });

        // if the generated image prompt says something along the lines of "I can't do that", then try again
        const isDecliningToasnwer = await generateIsDecliningToAnswer(generatedImagePrompt, message);
        if (isDecliningToasnwer.toLowerCase() === 'yes') {
            consoleLog('=', `Declining to answer, using text prompt instead`);
            generatedImagePrompt = textResponse;
        }
        
        consoleLog('=', `RESPONSE:\n\n${generatedImagePrompt}`);
        consoleLog('>');
        return generatedImagePrompt;
    },
    async generateNewConversation(
        queuedDatum,
        systemPrompt,
        localMongo,
        imagesCollection,
        repliedImagesCollection,
        repliedTranscription
    ) {
        const { message, recentMessages } = queuedDatum;
        const client = message.client;
        let recent100Messages = recentMessages.last(100);
        // Convert to array for index access
        const messagesArray = Array.from(recent100Messages.values());

        let modifiedSystemPrompt = systemPrompt;

        let conversation = [];

        const firstDate = luxon.DateTime.fromMillis(recent100Messages[0].createdTimestamp);
        const lastDate = luxon.DateTime.fromMillis(recent100Messages[recent100Messages.length - 1].createdTimestamp);


        let dateIdFormat = 'yyMMddHHmmSSS';
        if (firstDate.hasSame(lastDate, 'hour')) {
            dateIdFormat = 'mSSS';
        } else if (firstDate.hasSame(lastDate, 'day')) {
            dateIdFormat = 'HmmSSS';
        } else if (firstDate.hasSame(lastDate, 'month')) {
            dateIdFormat = 'dHHmmSSS';
        } else if (firstDate.hasSame(lastDate, 'year')) {
            dateIdFormat = 'MddHHmmSSS';
        }

        let index = 0;
        // Keep track of sequential user messages, and reset to 0 when we get an assistant message
        let userMessageXofY = 0;
        // Count total sequential messages in this group (backward + current + forward)
        let sequentialUserMessages = 1; // Start with current message

        for (let currentIndex = 0; currentIndex < messagesArray.length; currentIndex++) {
            const currentMessage = messagesArray[currentIndex];
            const isBot = currentMessage.author.id === client.user.id;
            if (isBot) {
                userMessageXofY = 0;
                // Assistant messages
                const assistantMessage = currentMessage;
                let assistantModifiedContent = assistantMessage.content;

                let imageDescription;
                let imageSize;
                let imageWidth;
                let imageHeight;

                if (assistantMessage?.attachments?.size > 0) {
                    const imageAttached = assistantMessage.attachments.find(attachment => attachment.contentType.includes('image'));
                    if (imageAttached) {
                        if (imageAttached.description) {
                            imageDescription = imageAttached.description;
                        } else if (imageAttached.title) {
                            imageDescription = imageAttached.title;
                        } else {
                            imageDescription = imageAttached.name.replace(/[_-]/g, ' ');
                        }

                        if (imageAttached.size) {
                            imageSize = imageAttached.size / 1024 / 1024; // Convert to MB
                        }

                        if (imageAttached.width && imageAttached.height) {
                            imageWidth = imageAttached.width;
                            imageHeight = imageAttached.height;
                        }
                    }
                }

                if (imageDescription) {
                    // assistantModifiedContent += `\n\n[image-reference: ${index}]`;
                    modifiedSystemPrompt += `\n\n # Images that you've generated and attached to your messages`;
                    modifiedSystemPrompt += `\n- Message content: ${assistantMessage.cleanContent}`;
                    modifiedSystemPrompt += `\n- Image description: ${imageDescription}`;
                    if (imageWidth && imageHeight) {
                        modifiedSystemPrompt += `\n- Dimensions: ${imageWidth}x${imageHeight}`;
                    }
                    if (imageSize) {
                        modifiedSystemPrompt += `\n- File size: ${imageSize.toFixed(2)} MB`;
                    }
                }

                conversation.push({
                    role: 'assistant',
                    name: UtilityLibrary.getUsernameNoSpaces(assistantMessage),
                    content: assistantModifiedContent,
                });
            } else {
                // Check if this starts a new sequence
                if (currentIndex === 0 || messagesArray[currentIndex - 1].author.id !== currentMessage.author.id) {
                    // This starts a new sequence - count forward only
                    userMessageXofY = 1;
                    
                    // Count total messages in this sequence (looking forward only)
                    sequentialUserMessages = 1;
                    for (let i = currentIndex + 1; i < messagesArray.length; i++) {
                        if (messagesArray[i].author.id === currentMessage.author.id) {
                            sequentialUserMessages++;
                        } else {
                            break;
                        }
                    }
                } else {
                    // This continues a sequence - we already know the total
                    userMessageXofY++;
                    // sequentialUserMessages stays the same for the entire sequence
                }
                
                // console.log(`User ${currentMessage.author.username}: Message ${userMessageXofY} of ${sequentialUserMessages} sequential messages`);



                const userMessage = currentMessage;
                const messageId = luxon.DateTime.fromMillis(userMessage.createdTimestamp).toFormat(dateIdFormat);
                // User messages
                const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member: userMessage.member });
                const messageSentAt = luxon.DateTime.fromMillis(userMessage.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
                const messageSentAtRelative = luxon.DateTime.fromMillis(userMessage.createdTimestamp).toRelative();

                // Replace mentions with names
                userMessage.content = userMessage.content.replace(/<@!?\d+>/g, (match) => {
                    const id = match.replace(/<@!?/, '').replace('>', '');
                    return UtilityLibrary.findUserById(client, id);
                });

                const messageContent = index === recent100Messages.length ? message.cleanContent : userMessage.cleanContent;

                let reactionsString;

                // if it's not the current message
                if (userMessage.reactions?.cache?.size > 0 && (userMessage.id !== message.id)) {
                    const reactions = userMessage.reactions.cache.map((reaction) => {
                        return `${reaction.emoji.name}`;
                    });
                    reactionsString = `${reactions.join(', ')}`;
                }

                const messageReferenceId = userMessage.reference?.messageId || null;

                let repliedMessage = null;
                if (userMessage.reference) {
                    repliedMessage = userMessage.channel.messages.cache.get(userMessage.reference.messageId);
                    if (!repliedMessage) {
                        try {
                            repliedMessage = await userMessage.channel.messages.fetch(userMessage.reference.messageId);
                        } catch (error) {
                            console.log(`Could not fetch replied message ${userMessage.reference.messageId}:`, error.message);
                        }
                    }
                }

                

                // Process URLs in the current user message
                const urls = userMessage.content.match(/(https?:\/\/[^\s]+)/g);
                let scrapedUrls;
                if (urls) {
                    scrapedUrls = await Promise.all(urls.map(async (url) => {
                        return await PuppeteerWrapper.scrapeURL(url);
                    }));
                }

                // is this the last message
                const isLastMessage = (currentIndex === messagesArray.length - 1);

                let modifiedContent = `=== MESSAGE ${userMessageXofY} of ${sequentialUserMessages} ${((userMessageXofY === sequentialUserMessages) && isLastMessage) ? '(MOST RECENT)' : ''} ===`;
                modifiedContent += `\n[METADATA]`;
                modifiedContent += `\nFrom: ${combinedNames}`;
                modifiedContent += `\nTime: ${messageSentAt} (${messageSentAtRelative})`;
                modifiedContent += `\nMessage ID: ${messageId}`;

                // REPLYING TO
                if (messageReferenceId) {
                    modifiedContent += `\n\n[REPLYING TO]`;
                    if (!repliedMessage) {
                        // If the reply has been deleted
                        modifiedContent += `\nAuthor: Unknown (DELETED MESSAGE)`;
                        modifiedContent += `\nMessage ID: ${messageReferenceId}`;
                    } else {
                        // If the reply exists
                        const replyMessageId = luxon.DateTime.fromMillis(repliedMessage.createdTimestamp).toFormat(dateIdFormat);
                        const combinedRepliedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member: repliedMessage.member });
                        modifiedContent += `\nAuthor: ${combinedRepliedNames}`;
                        modifiedContent += `\nTime: ${luxon.DateTime.fromMillis(repliedMessage.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a')} (${luxon.DateTime.fromMillis(repliedMessage.createdTimestamp).toRelative()})`;
                        modifiedContent += `\nMessage ID: ${replyMessageId}`;

                        if (repliedMessage.cleanContent) {
                            modifiedContent += `\nType: Text Message`;
                            modifiedContent += `\nContent:`;
                            modifiedContent += `\n<message_content>`;
                            modifiedContent += `\n${repliedMessage.content}`;
                            modifiedContent += `\n</message_content>`;
                        }

                        if (repliedTranscription) {
                            modifiedContent += `\nType: Voice Message`;
                            modifiedContent += `\nContent:`;
                            modifiedContent += `\n<audio_content>`;
                            modifiedContent += `\n${repliedTranscription}`;
                            modifiedContent += `\n</audio_content>`;
                        }

                        modifiedContent += await generateStickerResponse(repliedMessage, localMongo);
                        if (!repliedTranscription) {
                            modifiedContent += await generateAttachmentsResponse(repliedMessage, repliedImagesCollection, true);
                        }
                        modifiedContent += await generateEmojiResponse(repliedMessage, true);

                    }
                }
                modifiedContent += `\n\n[CURRENT MESSAGE]`;
                // TEXT MESSAGE
                if (messageContent) {
                    modifiedContent += `\nType: Text Message`;
                    modifiedContent += `\nContent:`;
                    modifiedContent += `\n<message_content>`;
                    modifiedContent += `\n${messageContent}`;
                    modifiedContent += `\n</message_content>`;
                    if (scrapedUrls?.length) {
                        modifiedContent += `\n\n[SCRAPED URL CONTENT]`;
                        scrapedUrls.forEach((scrapedData, index) => {
                            modifiedContent += `\n- ${urls[index]}:`;
                            for (const [key, value] of Object.entries(scrapedData)) {
                                if (value) {
                                    modifiedContent += `\n  - ${UtilityLibrary.capitalize(key)}: ${value}`;
                                }
                            }
                        });
                    }
                }

                let audioTranscriptions;
                let imageCaptions;

                // If it has attachments
                if (userMessage?.attachments?.size) {
                    const audioUrls = await DiscordUtilityService.extractAudioUrlsFromMessage(userMessage);
                    const imageUrls = await DiscordUtilityService.extractImageUrlsFromMessage(userMessage);

                    if (audioUrls?.length) {
                        // A string
                        audioTranscriptions = await transcribeAudio(audioUrls, userMessage.id, localMongo);
                    }

                    if (imageUrls.length) {
                        // An array or map
                        imageCaptions = await captionImages(imageUrls, userMessage.id, localMongo);
                    }

                    if (!messageContent) {
                        if (audioTranscriptions) {
                            modifiedContent += `\nType: Voice Message`;
                            modifiedContent += `\nAudio Content:`;
                            modifiedContent += `\n<audio_content>`;
                            modifiedContent += `\n${audioTranscriptions}`;
                            modifiedContent += `\n</audio_content>`;
                        }
                        if (!audioTranscriptions && imageCaptions?.length) {
                            modifiedContent += `\nType: Image Message`;
                            for (const [index, imageDescription] of imageCaptions.entries()) {
                                modifiedContent += `\nImage Content:`;
                                modifiedContent += `\n<image_content>`;
                                modifiedContent += `\n  ${imageDescription}`;
                                modifiedContent += `\n</image_content>`;
                            }
                        }
                    } else {
                        if (audioTranscriptions || imageCaptions?.length) {
                            modifiedContent += `\nAttachments (${imageCaptions.length}):`;
                        }
                        if (audioTranscriptions?.length) {
                            modifiedContent += `\nAudio Transcription: ${audioTranscriptions}`;
                        }
                        if (imageCaptions?.length) {
                            for (const [index, imageDescription] of imageCaptions.entries()) {
                                modifiedContent += `\nImage ${index} Description: ${imageDescription}`;
                            }
                        }
                    }
                }

                modifiedContent += await generateStickerResponse(userMessage, localMongo);
                // modifiedContent += await generateAttachmentsResponse(userMessage, imagesCollection, true);

                // If it contains reactions
                if (reactionsString) {
                    modifiedContent += `\nNumber of reactions in this message: ${userMessage.reactions.cache.size}`;
                    modifiedContent += `\nReaction list: ${reactionsString}`;
                }

                conversation.push({
                    role: 'user',
                    name: UtilityLibrary.getUsernameNoSpaces(userMessage),
                    content: modifiedContent
                })
            }
        }

        
        conversation.unshift({
            role: 'system',
            content: modifiedSystemPrompt
        });
        return conversation;
    },
};

module.exports = AIService;
