const config = require('../config');
const DiscordUtilityService = require('../services/DiscordUtilityService');
const UtilityLibrary = require('../libraries/UtilityLibrary');
const LightWrapper = require('../wrappers/LightWrapper');

const { slowBlink } = UtilityLibrary.ansiEscapeCodes(true);

const LogFormatter = {
    globalFormatter({
        // Required
        logEmoji,
        fileName,
        functionName,
        logName,

        client,
        user,
        member,
        guild,
        channel,
        message,
        role,
        reaction,
        state,
        interaction,
    
        systemPrompt,
        conversation,
        generatedText,
        duration,

        roleId,
        userId,
        
        error,

    }) {
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);

        let theClient = client;
        let theUser = user;
        let theMember = member;
        let theGuild = guild;
        let theChannel = channel;
        let theMessage = message;
        let theGuilds;
        let theInteractionCustom;

        if (reaction) {
            theUser = reaction.message.author;
            theMember = reaction.message.member;
            theGuild = reaction.message.guild;
            theChannel = reaction.message.channel;
            theMessage = reaction.message;
            theClient = reaction.message.client;
        }
        if (state) {
            theUser = state.member.user;
            theMember = state.member;
            theGuild = state.guild;
            theChannel = state.channel;
            theClient = state.client;
        }
        if (interaction) {
            theUser = interaction.user;
            theMember = interaction.member;
            theGuild = interaction.guild;
            theChannel = interaction.channel;
            theInteractionCustom = interaction.customId;
            theClient = interaction.client;
        }
        if (message) {
            theUser = message.author;
            theMember = message.member;
            theGuild = message.guild;
            theChannel = message.channel;
            theClient = message.client;
        }

        if (theClient) {
            // theGuilds = theClient.guilds.cache.map(aGuild => DiscordUtilityService.getCombinedGuildInformationFromGuild(aGuild)).join(' | ');
        }

        const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ user: theUser, member: theMember }, true);
        const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(theGuild, true);
        const combinedChannelInformation = DiscordUtilityService.getCombinedChannelInformationFromChannel(theChannel, true);
        const combinedEmojiInformation = DiscordUtilityService.getCombinedEmojiInformationFromReaction(reaction, true);
        const combinedRoleInformation = DiscordUtilityService.getCombinedRoleInformationFromRole(role, true)
        const time = UtilityLibrary.getCurrentDateAndTime();

        let log = `${logEmoji} [${fileName}:${functionName}] ${slowBlink(logName)}`;
        log += `\n    Time: ${time}`;
        // Duration
        if (duration) {
            log += `\n    Duration: ${duration} seconds`;
        }
        // Client and Guilds
        if (theClient) {
            log += `\n    Client: ${DiscordUtilityService.getCombinedNamesFromUserOrMember({ user: theClient.user }, true)}`;
            if (theGuilds) {
                log += `\n    Guilds: ${theGuilds}`;
            }
        }
        // User ID
        if (userId) {
            log += `\n    User ID: ${userId}`;
        }
        // Member or User
        if (combinedNames) {
            log += `\n    ${theMember ? `Member: ${combinedNames}` : `User: ${combinedNames}`}`;
        }
        // Role
        if (combinedRoleInformation) {
            log += `\n    Role: ${combinedRoleInformation}`;
        }
        if (roleId) {
            log += `\n    Role ID: ${roleId}`;
        }
        // Emoji
        if (combinedEmojiInformation) {
            log += `\n    Emoji: ${combinedEmojiInformation}`;
        }
        // Guild and Channel
        if (combinedGuildInformation) {
            log += `\n    Guild: ${combinedGuildInformation}`;
        }
        if (combinedChannelInformation) {
            log += `\n    Channel: ${combinedChannelInformation}`;
        }
        // Conversation and Messages
        if (systemPrompt) {
            log += `\n    System Prompt: ${{ systemPrompt: systemPrompt }}`;
        }
        if (conversation) {
            log += `\n    Conversation: ${{ conversation: conversation }}`;
        }
        if (generatedText) {
            log += `\n    Generated Text: ${{ generatedText: generatedText }}`;
        }
        if (theMessage) {
            if (theMessage.content) {
                log += `\n    Message Content: ${{ content: theMessage.content }}`;
            }
            log += `\n    Message URL: ${`https://discord.com/channels/${theMessage.guild.id}/${theMessage.channel.id}/${theMessage.id}`}`;
        }
        // Interaction
        if (theInteractionCustom) {
            log += `\n    Interaction Custom ID: ${theInteractionCustom}`;
        }
        // Error
        if (error) {
            log += `\n    Error: ${error}`;
        }
        return log;
    },
    generateFormatter({
        modelType,
        modelName,
        characterCount,
        duration,
        inputTokenCount,
    }) {
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
        const time = UtilityLibrary.getCurrentDateAndTime();
        let log = `🛠️ [OpenAIService:generateText] ${slowBlink('MODEL SETTINGS')}`;
        log += `\n    Time: ${time}`;
        log += `\n    Model Name: ${modelName}`;
        log += `\n    Model Type: ${modelType}`;
        log += `\n    Duration: ${duration} seconds`;
        log += `\n    Character Count: ${characterCount}`;
        if (inputTokenCount) {
            log += `\n    Input Token Count: ${inputTokenCount}`;
        }
        return log;
    },
    scrapeFormatter({
        url,
        result,
    }) {
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
        const time = UtilityLibrary.getCurrentDateAndTime();
        let log = `🌐 [PuppeteerWrapper:scrapeURL] ${slowBlink('SCRAPED URL')}`;
        log += `\n    Time: ${time}`;
        log += `\n    URL: ${url}`;
        
        if (result) {
            // iterate through the result object
            for (const [key, value] of Object.entries(result)) {
                log += `\n    ${UtilityLibrary.capitalize(key)}: ${value}`;
            }
        }

        return log;
    },
    reactionAdded(user, reaction) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👤➕👍!',
            fileName: 'DiscordService',
            functionName: 'processCreateReaction',
            logName: 'REACTION ADDED',
            reaction,
            user,
        });
    },
    messageReceived(message) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '📝',
            fileName: 'DiscordService',
            functionName: 'generateReply',
            logName: 'MESSAGE RECEIVED',
            message,
        });
    },

    memberJoinedGuild(member) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👤➡️🏰',
            fileName: 'DiscordService',
            functionName: 'luposOnGuildMemberAdd',
            logName: 'MEMBER JOINED GUILD',
            member,
        });
    },
    conversationGenerated(systemPrompt, conversation, generatedText, message) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '🧠',
            fileName: 'DiscordService',
            functionName: 'generateReply',
            logName: 'CONVERSATION GENERATED',
            systemPrompt,
            conversation,
            generatedText,
            message,
        });
    },
    receivedGuildMessage(message) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👥💬',
            fileName: 'DiscordService',
            functionName: 'receivedGuildMessage',
            logName: 'GUILD MESSAGE RECEIVED',
            message,
        });
    },
    receivedDirectMessage(message) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👤💬',
            fileName: 'DiscordService',
            functionName: 'receivedDirectMessage',
            logName: 'DIRECT MESSAGE RECEIVED',
            message,
        });
    },
    replyGuildMessageSuccess(message, generatedTextResponse, duration) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '➕📡💬',
            fileName: 'DiscordService',
            functionName: 'replyGuildMessageSuccess',
            logName: 'GUILD MESSAGE REPLIED',
            message,
            generatedTextResponse,
            duration,
        });
    },
    replyDirectMessageSuccess(message, generatedTextResponse, duration) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '➕💬',
            fileName: 'DiscordService',
            functionName: 'replyDirectMessageSuccess',
            logName: 'DIRECT MESSAGE REPLIED',
            message,
            generatedTextResponse,
            duration,
        });
    },
    memberLeftGuild(member) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '⬅️👤🏰',
            fileName: 'DiscordService',
            functionName: 'luposOnGuildMemberRemove',
            logName: 'MEMBER LEFT GUILD',
            member,
        });
    },
    memberJoinedVoiceChannel(newState) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👤➡️🎤',
            fileName: 'DiscordService',
            functionName: 'luposOnVoiceStateUpdate',
            logName: 'MEMBER JOINED VOICE CHANNEL',
            state: newState,
        });
    },
    memberLeftVoiceChannel(oldState) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '⬅️👤🎤',
            fileName: 'DiscordService',
            functionName: 'luposOnVoiceStateUpdate',
            logName: 'MEMBER LEFT VOICE CHANNEL',
            state: oldState,
        });
    },
    botReady(client) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '💡',
            fileName: 'DiscordService',
            functionName: 'luposOnReady',
            logName: 'BOT READY',
            client,
        });
    },
    memberUpdateOnboardingComplete(member) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '👤🎉🚀',
            fileName: 'DiscordService',
            functionName: 'luposOnGuildMemberUpdate',
            logName: 'MEMBER ONBOARDING COMPLETE',
            member,
        });
    },
    interactionCreate(interaction) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '⭐',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'INTERACTION CREATED',
            interaction,
        });
    },
    interactionCreateButton(interaction) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '🕹️',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'INTERACTION TYPE: BUTTON',
            interaction,
        });
    },
    interactionCreateCommand(interaction) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '⭐',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'INTERACTION TYPE: COMMAND',
            interaction,
        });
    },
    commandNotFound(interaction) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'COMMAND NOT FOUND',
            interaction,
        });
    },
    commandError(interaction, error) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'COMMAND ERROR',
            interaction,
            error,
        });
    },
    roleNotFound(interaction, roleId) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'ROLE NOT FOUND',
            interaction,
            roleId,
        });
    },
    memberNotFound(interaction, roleId) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'MEMBER NOT FOUND',
            interaction,
            roleId,
        });
    },
    roleRemoved(member, role) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '➖🏷️',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'ROLE REMOVED',
            member,
            role,
        });
    },
    roleAdded(member, role) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '➕🏷️',
            fileName: 'DiscordService',
            functionName: 'luposOnInteractionCreate',
            logName: 'ROLE ADDED',
            member,
            role,
        });
    },
    timeOutMemberNotFound(user, guild) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'PermanentTimeOutJob',
            functionName: 'timeOutUsers',
            logName: 'TIMEOUT MEMBER NOT FOUND',
            user,
            guild,
        });
    },
    timeOutUserDoesntExist(userId) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'PermanentTimeOutJob',
            functionName: 'timeOutUsers',
            logName: `TIMEOUT USER DOESN'T EXIST`,
            userId,
        });
    },
    timeOutMember(member, guild, duration) {
        return LogFormatter.globalFormatter({ 
            logEmoji: '⏰',
            fileName: 'PermanentTimeOutJob',
            functionName: 'timeOutUsers',
            logName: 'TIMEOUT MEMBER',
            member,
            guild,
            totalTime: duration,
        });
    },
    comfyUITimedOut() {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'ComfyUIWrapper',
            functionName: 'checkWebsocketStatus',
            logName: 'COMFY UI TIMED OUT',
        });
    },
    comfyUIDown() {
        return LogFormatter.globalFormatter({ 
            logEmoji: '❌',
            fileName: 'ComfyUIWrapper',
            functionName: 'checkWebsocketStatus',
            logName: 'COMFY UI DOWN',
        });
    },
    comfyUIUp() {
        return LogFormatter.globalFormatter({ 
            logEmoji: '✅',
            fileName: 'ComfyUIWrapper',
            functionName: 'checkWebsocketStatus',
            logName: 'COMFY UI UP',
        });
    },
}

module.exports = LogFormatter;
