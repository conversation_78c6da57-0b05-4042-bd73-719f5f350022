require('dotenv/config');
const luxon = require('luxon');
const {
    Collection,
    ChannelType,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ActivityType,
    MessageFlags
} = require('discord.js');
const { GetColorName } = require('hex-color-to-color-name');
const moment = require('moment');
const fs = require('node:fs');
const path = require('node:path');
// CONFIG
const config = require('../config.json');
// ARRAYS
const {
    rolesVideogames,
    warcraftClasses,
    warcraftFactions 
} = require('../arrays/roles.js');
const channels = require('../arrays/channels.js');
// WRAPPERS
const PuppeteerWrapper = require('../wrappers/PuppeteerWrapper.js');
const DiscordWrapper = require('../wrappers/DiscordWrapper.js');
const YouTubeWrapper = require('../wrappers/YouTubeWrapper.js');
const LightWrapper = require('../wrappers/LightWrapper.js');
const ComfyUIWrapper = require('../wrappers/ComfyUIWrapper.js');
// SERVICES
const DiscordUtilityService = require('./DiscordUtilityService.js');
const MessageService = require('./MessageService.js');
const AIService = require('./AIService.js');
// JOBS
const BirthdayJob = require('../jobs/scheduled/BirthdayJob.js');
const ChatterJob = require('../jobs/scheduled/ChatterJob.js');
const ActivityRoleAssignmentJob = require('../jobs/scheduled/ActivityRoleAssignmentJob.js');
// const ReactJob = require('../jobs/scheduled/ReactJob.js');
const RemindersJob = require('../jobs/scheduled/RemindersJob.js');
const PermanentTimeOutJob = require('../jobs/scheduled/PermanentTimeOutJob.js');
const EventReactJob = require('../jobs/event-driven/ReactJob.js');
// LIBRARIES
const { consoleLog } = require('../libraries/UtilityLibrary.js');
const MessageConstant = require('../constants/MessageConstants.js');
const UtilityLibrary = require('../libraries/UtilityLibrary.js');
const crypto = require('crypto');
const LogFormatter = require('../formatters/LogFormatter.js');



let lastMessageSentTime = luxon.DateTime.now().toISO()
let isProcessingQueue = false;
const queuedData = [];
// QUEUE: Reactions
let isProcessingOnReactionQueue = false;
const reactionQueue = [];
const allUniqueUsers = {};
const reactionMessages = {};
let typingIntervals = {};

function updateLastMessageSentTime() {
    setInterval(() => {     
        let currentTime = luxon.DateTime.now();
        let lastMessageSentTimeObject = luxon.DateTime.fromISO(lastMessageSentTime);
        let difference = currentTime.diff(lastMessageSentTimeObject, ['seconds']).toObject();
        if (difference.seconds >= 30) {
            lastMessageSentTime = currentTime.toISO();
        }
    }, 1000);
    return lastMessageSentTime;
}

async function extractEmojis(message) {
    const emojis = [];
    const messageEmojis = message.content.split(' ').filter(part => /<(a)?:.+:\d+>/g.test(part)) || [];
    if (messageEmojis) {
        for (const emoji of messageEmojis) {
            const parsedEmoji = emoji.replace(/[\n#]/g, '');
            const emojiId = parsedEmoji.split(":").pop().slice(0, -1);
            const emojiName = parsedEmoji.match(/:.+:/g)[0].replace(/:/g, '');
            const emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.png`;
            const { response } = await AIService.generateVision(emojiUrl, `Describe this image named ${emojiId}. Do not mention that it is low quality, resolution, or pixelated.`);
            
            const emojiAttached = {
                id: emojiId,
                tag: parsedEmoji,
                name: emojiName,
                url: emojiUrl,
                description: response.choices[0].message.content
            };

            emojis.push(emojiAttached);
        }
    }
    return emojis;
}

async function generateDescription(systemPrompt, message, participant, who, participantIndex, messages) {
    const user = participant.user;
    const member = participant.member;
    const conversation = participant.conversation;

    if (!user) {
        console.error(`❌ [DiscordService:generateDescription] No user found for participant:`, participant);
        return systemPrompt;
    }

    let messageSentAt;
    let messageSentAtRelative;
    const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member });

    if (messages?.size) {
        const lastMessageSentByUser = messages.find(msg => msg.author.id === user.id);
        if (lastMessageSentByUser) {
            messageSentAt = luxon.DateTime.fromMillis(lastMessageSentByUser?.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
            messageSentAtRelative = luxon.DateTime.fromMillis(lastMessageSentByUser?.createdTimestamp).toRelative();
        }
    }

    if (who === 'PRIMARY') {
        systemPrompt += `\n\n# About me: ${combinedNames}`;
        systemPrompt += `\n- PRIMARY TARGET: You're replying to me only (aware of others but ignore them)`;
    } else if (who === 'SECONDARY' || who === 'MENTIONED') {
        systemPrompt += `\n\n# ${participantIndex}. ${combinedNames}`;
        systemPrompt += `\n- SECONDARY TARGET: You're aware of others but ignore them (reply to me only)`;
    }

    if (user?.id) { systemPrompt += `\n- Discord user ID: ${user.id}` }
    if (member?.nickname) { systemPrompt += `\n- Nickname: ${member?.nickname}` } // Server-specific nickname
    if (user?.globalName) { systemPrompt += `\n- Name: ${user.globalName}` } // Discord nickname
    if (user?.username) { systemPrompt += `\n- Username: ${user.username}` } // Discord username

    if (member?.presence?.status) {
        systemPrompt += `\n- Status: ${member.presence.status}`; // online, idle, dnd, offline
        if (member.presence.status === 'online') {
            if (member?.presence?.clientStatus) {
                const platforms = Object.keys(member.presence.clientStatus);
                systemPrompt += `\n- Active on: ${platforms.join(', ')}`;
            }
        }
    }
    if (member?.presence?.activities?.length > 0) {
        const customStatus = member.presence.activities.find(a => a.type === 4);
        if (customStatus?.state) {
            systemPrompt += `\n- Custom status: "${customStatus.state}"`;
        }
        
        // Current activities (playing games, listening to music, etc.)
        const activities = member.presence.activities
            .filter(a => a.type !== 4)
            .map(a => {
                const types = ['Playing', 'Streaming', 'Listening to', 'Watching', 'Custom', 'Competing'];
                return `${types[a.type]} ${a.name}`;
            });
        if (activities.length > 0) {
            systemPrompt += `\n- Activities: ${activities.join(', ')}`;
        }
    }

    if (user?.accentColor) {
        // User must be force-fetched to get this property
        const toHex = d => "#" + d.toString(16).padStart(6, '0').toUpperCase();
        const hexColor = toHex(user.accentColor);
        const colorName = GetColorName ? GetColorName(hexColor) : hexColor;
        systemPrompt += `\n- Profile color (their choice of color): ${colorName} (${hexColor})`;
    }

    const accountCreatedAt = luxon.DateTime.fromMillis(user.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
    const accountCreatedAtRelative = luxon.DateTime.fromMillis(user.createdTimestamp).toRelative();
    systemPrompt += `\n- Account creation date: ${accountCreatedAt} (${accountCreatedAtRelative})`;
    if (messageSentAt && messageSentAtRelative) {
        systemPrompt += `\n- Last message sent on: ${messageSentAt} (${messageSentAtRelative})`;
    }
        
    // is timed out
    if (member?.communicationDisabledUntilTimestamp) {
        if (member.communicationDisabledUntilTimestamp > Date.now()) {
            systemPrompt += `\n- Timed out until: ${luxon.DateTime.fromMillis(member.communicationDisabledUntilTimestamp).toRelative()}`;
        } else {
            systemPrompt += `\n- Last timed out at: ${luxon.DateTime.fromMillis(member.communicationDisabledUntilTimestamp).toRelative()}`;
        }
    }
    // when they joined the server
    if (member) {
        const serverJoinDateAt = luxon.DateTime.fromMillis(member.joinedTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
        const serverJoinDateRelative = luxon.DateTime.fromMillis(member.joinedTimestamp).toRelative();
        systemPrompt += `\n- Join date: ${serverJoinDateAt} (${serverJoinDateRelative})`;
    }
    // is boosting the server
    if (member?.premiumSinceTimestamp) {
        const boostDateAt = luxon.DateTime.fromMillis(member.premiumSinceTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
        const boostDateRelative = luxon.DateTime.fromMillis(member.premiumSinceTimestamp).toRelative();
        systemPrompt += `\n- Boosting since: ${boostDateAt} (${boostDateRelative})`;
    }

    // + Permissions
    if (member?.permissions?.has('Administrator')) {
        systemPrompt += `\n- Has administrator permissions`;
    }
    const modPerms = ['ManageMessages', 'KickMembers', 'BanMembers', 'ManageRoles'];
    const hasModPerms = modPerms.filter(perm => member?.permissions?.has(perm));
    if (hasModPerms.length > 0) {
        systemPrompt += `\n- Moderation permissions: ${hasModPerms.join(', ')}`;
    }
    const channelPerms = member && message.channel ? member.permissionsIn(message.channel) : null;
    if (channelPerms) {
        if (!channelPerms.has('SendMessages')) {
            systemPrompt += `\n- Cannot send messages in this channel`;
        }
        if (!channelPerms.has('ViewChannel')) {
            systemPrompt += `\n- Cannot view this channel (but was mentioned)`;
        }
    }
    // - Permissions

    if (!member) {
        systemPrompt += `\n- They have left the server and are no longer in the chat because they ran away.`;
    } else {
        // + Manageable
        if (member.kickable) {
            systemPrompt += `\n- You can kick or ban them from the server`;
        } else {
            systemPrompt += `\n- You cannot kick or ban them from the server. You do not have permission to do so.`;
        }
        if (member.manageable) {
            systemPrompt += `\n- You can manage this user's roles`;
        } else {
            systemPrompt += `\n- You cannot manage this user's roles.`;
        }
        // - Manageable
        // + Server Roles
        if (member.roles?.cache.size > 1) {
            systemPrompt += `\n- Roles: ${member.roles.cache.filter(role => role.name !== '@everyone').map(role => role.name).join(', ')}`;
            if (member.roles.highest) {
                systemPrompt += `\n- Highest role: ${member.roles.highest.name}`;
                // systemPrompt += `\n- Highest role: ${member.roles.highest.name} (position: ${member.roles.highest.position})`;
            }
        } else {
            systemPrompt += `\n- Roles: No roles`;
        }
        // - Server Roles
        if (member.displayHexColor) {
            systemPrompt += `\n- Highest role color (automatically assigned): ${GetColorName(member?.displayHexColor)} (${member.displayHexColor})`;
        }
    }


    // is it a bot
    if (user.bot) {
        systemPrompt += `\n- They are a bot`;
    }

    // + Voice Channel Details
    if (member?.voice?.channel) {
        systemPrompt += `\n- In voice channel: ${member.voice.channel.name}`;
        if (member.voice.deaf || member.voice.selfDeaf) {
            systemPrompt += `\n- Deafened in voice`;
        }
        if (member.voice.mute || member.voice.selfMute) {
            systemPrompt += `\n- Muted in voice`;
        }
        if (member.voice.streaming) {
            systemPrompt += `\n- Streaming in voice`;
        }
        if (member.voice.cameraOn) {
            systemPrompt += `\n- Camera on in voice`;
        }
        if (member.voice.suppress) {
            systemPrompt += `\n- Suppressed in voice`;
        }
        if (member.voice.requestedToSpeak) {
            systemPrompt += `\n- Requested to speak in voice`;
        }
    }
    // - Voice Channel Details

    if (who === 'PRIMARY') {
        systemPrompt += `\n\n## My conversation summary `;
        systemPrompt += `\n${conversation}`;
    } else if (who === 'SECONDARY') {
        systemPrompt += `\n\n## The conversation summary of ${combinedNames}`;
        systemPrompt += `\n${conversation}`;
    }
    return systemPrompt;
}

async function generateReply(
    queuedDatum,
    mentionsCollection,
    imagesCollection,
    emojisAttached,
    replies,
    mentionedNameDescriptions,
    scrapedUrls,
    participantsCollection,
    canGenerateImage,
    repliedImagesCollection,
    repliedTranscription,
    localMongo
) {
    const { message, recentMessages } = queuedDatum;
    const client = message.client;
    let systemPrompt;
    let imagePrompt;
    let generatedText;
    let serverContext = [];
    try {
        console.log(LogFormatter.messageReceived(message));

        if (message.guildId === config.GUILD_ID_WHITEMANE ||
            message.guildId === '1357934319879979170' // lupos logs
        ) {
            // If any of the recent messages match the custom context keywords...
            // ... or if the name of the user matches the custom context keywords
            const customContextWhitemane = MessageConstant.customContextWhitemane;
            const serverContextSet = new Set();
            
            const contextWithPatterns = customContextWhitemane.map(context => {
                const keywords = Array.isArray(context.keywords) 
                    ? context.keywords 
                    : context.keywords.split(/[,\s]+/);
                const patterns = keywords.map(keyword => 
                    new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i')
                );
                return { context, patterns };
            });
            
            for (const recentMessage of recentMessages.values()) {
                let searchText = `${recentMessage.cleanContent}`;

                if (recentMessage.author) {
                    searchText += ` ${recentMessage.author?.globalName} ${recentMessage.author?.username} ${recentMessage.author?.displayName}`;
                }

                searchText = searchText.toLowerCase();

                for (const { context, patterns } of contextWithPatterns) {
                    if (patterns.some(pattern => pattern.test(searchText))) {
                        serverContextSet.add(context);
                    }
                }
            }
            serverContext.push(...serverContextSet);
        }

        imagePrompt = String(message.content);

        systemPrompt = '';
        systemPrompt += `${MessageService.assembleAssistantMessage(canGenerateImage, message.guildId)}`;
        systemPrompt += `\n\n# Discord client information`;
        systemPrompt += `\n- Your name: ${DiscordUtilityService.getCombinedNamesFromUserOrMember({ user: client.user })}`;
        systemPrompt += `\n- Your discord user ID tag: <@${client.user.id}>`;
        // systemPrompt += `\n- The date is ${moment().format('MMMM Do YYYY')}`;
        // systemPrompt += `\n- The day is ${moment().format('dddd')}`;
        // systemPrompt += `\n- The time is ${moment().format('h:mm A')} in PST.`;
        systemPrompt += `\n- The current date and time is ${moment().format('dddd, MMMM Do, YYYY at h:mm A')} PST.`;
        systemPrompt += `\n- To mention, tag or reply to someone, you do it by mentioning their Discord user ID tag. For example, to mention me, you would type <@${client.user.id}>.`;

        if (message.guild) {
            const bans = await message.guild.bans.fetch();

            systemPrompt += `\n\n# Discord server information`;
            // GUILD NAME
            systemPrompt += `\n- You are in the discord server called: ${message.guild.name}.`
            // CREATED AT
            const createdAtTimestampAt = luxon.DateTime.fromMillis(message.guild.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
            const createdAtTimestampRelative = luxon.DateTime.fromMillis(message.guild.createdTimestamp).toRelative();
            systemPrompt += `\n- This server was created on: ${createdAtTimestampAt} (${createdAtTimestampRelative})`;
            // DESCRIPTION
            if (message.guild.description) {
                systemPrompt += `\n- The server description is: ${message.guild.description}`;
            }
            // SERVER HAS
            systemPrompt += `\n- This server has:`;
            systemPrompt += `\n  - ${message.guild.memberCount} members`;
            systemPrompt += `\n  - ${message.guild.channels.cache.size} channels`;
            systemPrompt += `\n  - ${message.guild.premiumSubscriptionCount} server nitro boosts`;
            if (message.guild.mfaLevel === 1) {
                systemPrompt += `\n  - 2FA enabled`;
            }
            // mfaLevel
            // commands
            if (message.guild.commands.cache.size) {
                systemPrompt += `\n  - ${message.guild.commands.cache.size} commands:`;
                for (const command of message.guild.commands.cache.values()) {
                    systemPrompt += `\n    - ${command.name} (${command.description})`;
                }
            }
            // bans
            if (bans.size) {
                systemPrompt += `\n  - ${bans.size} bans`;
            }
            // emojis
            if (message.guild.emojis.cache.size) {
                systemPrompt += `\n  - ${message.guild.emojis.cache.size} emojis`;
                // const serverEmojis = await DiscordUtilityService.getAllServerEmojisFromMessage(message, 'string');
                // systemPrompt += `\n- The server emojis are: ${serverEmojis}`;
                // systemPrompt += `\n- You can use any of these emojis in your response by typing the emoji name.`;
            }
            // roles
            if (message.guild.roles.cache.size) {
                systemPrompt += `\n  - ${message.guild.roles.cache.size} roles`;
            }
            // owner
            if (message.guild.ownerId) {
                // const owner = await message.guild.members.cache.get(message.guild.ownerId);
                // const ownerUsername = owner ? owner.displayName || owner.user.username : 'Unknown';
                // systemPrompt += `\n- The server owner is: ${message.guild.ownerId}`;
            }

            // who is in voice chat
            const voiceChannelMembers = message.guild.channels.cache.filter(channel => channel.type === 'GUILD_VOICE' && channel.members.size > 0);
            if (voiceChannelMembers.size) {
                systemPrompt += `\n- The following voice channels have members in them:`;
                for (const channel of voiceChannelMembers.values()) {
                    systemPrompt += `\n  - ${channel.name} (${channel.members.size} members)`;
                    for (const member of channel.members.values()) {
                        systemPrompt += `\n    - ${DiscordUtilityService.getCombinedNamesFromUserOrMember({ member })}`;
                    }
                }
            }

        }
        if (message?.channel) {
            systemPrompt += `\n\n# Discord channel information`;
            systemPrompt += `\n- You are in the channel called: ${message.channel.name}`;
            if (message.channel.topic) {
                systemPrompt += `\n- The channel topic is: ${message.channel.topic}.`
            }
            const channelCreatedAt = luxon.DateTime.fromMillis(message.channel.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
            const channelCreatedAtRelative = luxon.DateTime.fromMillis(message.channel.createdTimestamp).toRelative();
            systemPrompt += `\n- This channel was created on: ${channelCreatedAt} (${channelCreatedAtRelative})`;
            const lastMessageSentAt = luxon.DateTime.fromMillis(message.channel.lastMessage?.createdTimestamp || message.createdTimestamp).setZone('local').toFormat('LLLL dd, yyyy \'at\' hh:mm:ss a');
            const lastMessageSentAtRelative = luxon.DateTime.fromMillis(message.channel.lastMessage?.createdTimestamp || message.createdTimestamp).toRelative();
            systemPrompt += `\n- Last message in this channel sent at: ${lastMessageSentAt} (${lastMessageSentAtRelative})`;
        }

        if (imagesCollection?.size) {
            for (const image of imagesCollection.values()) {
                if (image.imageDescription) {
                    imagePrompt += `\n\n${image.imageDescription}.`;
                }
            }
        }

        if (replies?.length) {
            systemPrompt += '\n\n## Primary participant is responding to another user while mentioning you in their reply';
            systemPrompt += `\nQuoted user: ${replies[0].name}`
            systemPrompt += `\n${replies[0].name}'s Discord user ID tag: <@${replies[0].userId}>`
            systemPrompt += `\n${replies[0].name}'s message: ${replies[0].content}`

            imagePrompt += `\n\n ${replies[0].content}`
        }
        if (participantsCollection?.size) {
            const primaryParticipant = participantsCollection.get(message.author?.id);
            if (primaryParticipant && primaryParticipant.user) {
                systemPrompt = await generateDescription(systemPrompt, message, primaryParticipant, 'PRIMARY');
            }
        }
        if (mentionsCollection?.size) {
            systemPrompt += `\n\n# Mentioned users (${mentionsCollection.size})`;
            let currentUserCount = 0;
            for (const mention of mentionsCollection.values()) {
                currentUserCount++;
                const user = mention.user;
                const member = mention.member;

                if (!user || !user.id) {
                    console.error(`❌ [DiscordService:generateReply] No user found for mention:`, mention);
                    continue;
                }

                const avatarDescription = mention.avatarDescription;
                const bannerDescription = mention.bannerDescription;

                systemPrompt = await generateDescription(systemPrompt, message, mention, 'MENTIONED', currentUserCount);

                if (avatarDescription) {
                    systemPrompt += `\n- Avatar description: ${avatarDescription}`;
                }
                if (bannerDescription) {
                    systemPrompt += `\n- Banner description: ${bannerDescription}`;
                }

                let userVisualDescription = ``;
                if (avatarDescription && bannerDescription) {
                    userVisualDescription = `(Subject: ${avatarDescription} + In front of: ${bannerDescription})`;
                } else if (avatarDescription) {
                    userVisualDescription = `(Subject: ${avatarDescription})`;
                } else if (bannerDescription) {
                    userVisualDescription = `(In front of: ${bannerDescription})`;
                }

                imagePrompt = imagePrompt.replace(`<@${user.id}>`, `${DiscordUtilityService.getCombinedNamesFromUserOrMember({ member })} ${userVisualDescription}`);
                imagePrompt = imagePrompt.replace(/\bme\b/gi, `me ${userVisualDescription}`);
                imagePrompt = imagePrompt.replace(/\bI\b/gi, `I ${userVisualDescription}`);
            }
        }

        if (serverContext?.length) {
            systemPrompt += '\n\n# Relevant context for this conversation';
            serverContext.forEach((context, index) => {
                systemPrompt += `\n\n# ${context.title}`;
                systemPrompt += `\n- Keywords: ${context.keywords}`;
                systemPrompt += `\n- Description: ${context.description}`;
                imagePrompt += `\n\n${context.description}.`;
            });
        }

        if (emojisAttached?.length) {
            emojisAttached.forEach((emoji) => {
                systemPrompt += `\n\n# Emojis Attached`;
                systemPrompt += `\nEmoji name: ${emoji.name}`;
                systemPrompt += `\nEmoji Discord tag: ${emoji.tag}`;
                systemPrompt += `\nEmoji description: ${emoji.description}`;

                imagePrompt = imagePrompt.replace(emoji.tag, `${emoji.name} (${emoji.description}).`);
            });
        }
        if (participantsCollection?.size) {
            if (participantsCollection.size > 1) {
                systemPrompt += `\n\n# Secondary participants (${participantsCollection.size - 1})`;
                let currentUserCount = 0;
                for (const participant of participantsCollection.values()) {
                    if (!participant?.user?.id || participant.user.id === message.author.id) continue;
                    currentUserCount++;
                    systemPrompt = await generateDescription(systemPrompt, message, participant, 'SECONDARY', currentUserCount, recentMessages);
                }
            }
        }
        if (imagePrompt?.includes(`<@${client.user?.id}`)) {
            imagePrompt = imagePrompt.replace(`<@${client.user.id}>`, '');
        }
        if (scrapedUrls) {
            if (scrapedUrls?.title) {
                imagePrompt += `\n\nThe title of the URL attached: ${scrapedUrls.title}.`;
            }
            if (scrapedUrls?.description) {
                imagePrompt += `\n\nThe description of the URL attached: ${scrapedUrls.description}.`;
            }
            if (scrapedUrls?.text) {
                imagePrompt += `\n\nThe text of the URL attached: ${scrapedUrls.text}.`;
            }
            if (scrapedUrls?.keywords) {
                imagePrompt += `\n\nThe keywords of the URL attached: ${scrapedUrls.keywords}.`;
            }
            if (scrapedUrls?.header) {
                imagePrompt += `\n\nThe header of the URL attached: ${scrapedUrls.header}.`;
            }

            if (scrapedUrls?.transcript) {
                imagePrompt += `\n\nThe transcript of the URL attached: ${scrapedUrls.transcript}.`;
            }
        }

        systemPrompt += `\n\n${MessageService.assembleBackstoryMessage(message.guild?.id)}`;
        systemPrompt += `\n\n${MessageService.assemblePersonalityMessage()}`;

        const conversation = await AIService.generateNewConversation(
            queuedDatum,
            systemPrompt,
            localMongo,
            imagesCollection,
            repliedImagesCollection,
            repliedTranscription
        );

        const generationSettings = {
            conversation: conversation,
            type: config.LANGUAGE_MODEL_TYPE,
            performance: 'POWERFUL',
            tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
            temperature: config.LANGUAGE_MODEL_TEMPERATURE
        }

        // if guild id is the clock crew, use fast anthropic model
        if (message.guildId === config.GUILD_ID_THE_CLOCK_CREW) {
            generationSettings.type = 'ANTHROPIC';
            generationSettings.performance = 'FAST';

        }

        generatedText = await AIService.generateText(generationSettings);
        // Clean response
        generatedText = UtilityLibrary.removeMentions(generatedText);
        generatedText = UtilityLibrary.removeFlaggedWords(generatedText);

        console.log(LogFormatter.conversationGenerated(systemPrompt, conversation, generatedText, message));
    } catch (error) {
        generatedText = '...',
        imagePrompt = 'an image of a beautiful purple wolf sleeping under the full moonlight, in the style of a watercolor painting. The text "Lupos sleeps under the full moonlight" is written in a beautiful cursive font at the bottom of the image.';
        console.error(`❌ [DiscordService:generateReply] Error: ${error}`);
    }
    return { generatedText, imagePrompt, systemPrompt };
}

async function generateNewTextResponsePart2(queuedDatum, systemPrompt, localMongo, imagePrompt, oldGeneratedText) {
    consoleLog('<');
    const { message } = queuedDatum;
    const client = message.client;
    let generatedText;

    // add image prompt and original response to the end of system prompt
    systemPrompt += `\n\n# Generate Enhanced Response with Visual Context`;
    systemPrompt += `\n\nYou previously generated this response: "${oldGeneratedText}"`;
    systemPrompt += `\n\nAn image was created based on your response using this prompt: "${imagePrompt}"`;
    systemPrompt += `\n\n## Your Task:`;
    systemPrompt += `\nRewrite your original response to incorporate visual details from the image prompt while preserving the core message and information.`;
    systemPrompt += `\n\n## Guidelines:`;
    systemPrompt += `\n- Maintain the same overall meaning and key points from your original response`;
    systemPrompt += `\n- Add sensory details, visual descriptions, and scene-setting based on the image prompt`;
    systemPrompt += `\n- Make the response feel like it's describing or referencing the generated image`;
    systemPrompt += `\n- Keep the same tone and style as the original`;
    systemPrompt += `\n- Do not mention that an image was generated or reference the process itself`;
    systemPrompt += `\n\n## Example approach:`;
    systemPrompt += `\n- If the original mentioned a concept, now describe how it appears in the image`;
    systemPrompt += `\n- If the original was abstract, ground it with visual elements from the image`;
    systemPrompt += `\n- Add colors, textures, composition, and atmosphere suggested by the image prompt`;


    // consoleLog('=', `generateNewTextResponsePart2 SYSTEM PROMPT:\n\n${systemPrompt}`);

    if (config.DEBUG_MODE) {
        // UtilityLibrary.consoleInfoColor([[`🧠 System Prompt2:\n${systemPrompt}`, { color: 'blue' }, 'middle']]);
    }

    try {
        const conversation = await AIService.generateNewConversation(
            queuedDatum,
            systemPrompt,
            localMongo
        );
        generatedText = await AIService.generateText({
            conversation: conversation,
            type: config.LANGUAGE_MODEL_TYPE,
            performance: 'POWERFUL',
            tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
            temperature: config.LANGUAGE_MODEL_TEMPERATURE
        });

        // clean response
        generatedText = UtilityLibrary.removeMentions(generatedText);
        generatedText = UtilityLibrary.removeFlaggedWords(generatedText);
        // remove self mention
        
        generatedText = generatedText.replace(new RegExp(`<@!?${client.user.id}>`, 'g'), '');
        
        if (config.DEBUG_MODE) {
            // UtilityLibrary.consoleInfoColor([[`🎨 generateTextResponse2 output:\n${generatedText}`, { color: 'green' }, 'middle']]);
        }

        consoleLog('=', `RESPONSE:\n\n${generatedText}`);
        consoleLog('>');
    } catch (error) {
        generatedText = '...';
        imagePrompt = 'an image of a beautiful purple wolf sleeping under the full moonlight, in the style of a watercolor painting. The text "Lupos sleeps under the full moonlight" is written in a beautiful cursive font at the bottom of the image.';
        
        consoleLog('=', `RESPONSE:\n\n${error}`);
        consoleLog('>!');
    }
    return { generatedText };
}

async function generateImageAndResponse(
    queuedDatum, localMongo, imagePrompt, generatedText, systemPrompt, mentionsCollection
) {
    consoleLog('<');
    let image = null;
    let text = generatedText;
    let newImagePrompt = null;

    if (config.GENERATE_IMAGE) {
        try {
            await ComfyUIWrapper.checkComfyUIWebsocketStatus();
            newImagePrompt = await AIService.createImagePromptFromImageAndText(
                queuedDatum,
                imagePrompt,
                generatedText,
                mentionsCollection
            );
            consoleLog('=', `Image Prompt: \n${newImagePrompt}`);
            LightWrapper.setState({ color: 'purple' }, config.PRIMARY_LIGHT_ID);
    
            if (newImagePrompt) {
                const imageUrl = ''; // This needs to be grabbed from the queuedDatum or another source if available
                // Start both operations
                const imagePromise = (imageUrl 
                    ? AIService.generateImageToImage(newImagePrompt, imageUrl)
                    : AIService.generateImage(newImagePrompt)
                ).then(result => {
                    consoleLog('=', `RESPONSE:\n\n🖼️`);
                    return result;
                });
                
                const textPromise = generateNewTextResponsePart2(
                    queuedDatum,
                    systemPrompt,
                    localMongo,
                    newImagePrompt,
                    generatedText
                ).then(result => {
                    consoleLog('=', `RESPONSE:\n\n${result.generatedText}`);
                    return result;
                });

                // Wait for both, handling errors individually
                try {
                    image = await imagePromise;
                } catch (imageError) {
                    consoleLog('=', `Image generation failed: ${imageError}`);
                }

                try {
                    const textResult = await textPromise;
                    text = textResult.generatedText;
                } catch (textError) {
                    consoleLog('=', `Text generation failed: ${textError}`);
                }

                consoleLog('>');
                LightWrapper.setState({ color: 'yellow' }, config.PRIMARY_LIGHT_ID);
            }
        } catch (error) {
            consoleLog('=', `RESPONSE:\n\n${error}`);
            consoleLog('>!');
        }
    }
    return { image, text, newImagePrompt }
}

async function replyMessage(queuedDatum, localMongo) {
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    const message = queuedDatum.message;
    const messages = queuedDatum.recentMessages;
    const client = message.client;
    const guild = message.guild;
    const channel = message.channel;
    const content = message.content;
    const bot = client.user;
    const member = message.member;
    const user = message.author;

    // We need to pass both member and user to handle both server messages and private messages
    const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member, user });
    let combinedGuildInformation;
    let combinedChannelInformation;

    let timer = 0;
    const timerInterval = setInterval(() => { timer++ }, 1000);

    if (guild) {
        combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(guild);
        combinedChannelInformation = DiscordUtilityService.getCombinedChannelInformationFromChannel(channel);
        console.log(LogFormatter.receivedGuildMessage(message));
    } else {
        console.log(LogFormatter.receivedDirectMessage(message));
    }

    let generatedTextResponse;
    let generatedImage;

    let canGenerateImage = false;
    // CHECK IF WE CAN GENERATE AN IMAGE
    try {
        await ComfyUIWrapper.checkComfyUIWebsocketStatus();
        canGenerateImage = true;
    } catch (error) {
        canGenerateImage = false;
    }
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // GENERATE A CUSTOM EMOJI REACTION
    const customEmojiReact = await AIService.generateCustomEmojiReactFromMessage(message);
    if (customEmojiReact) {
        try {
            await message.react(customEmojiReact);
        } catch (error) {
            // 
        }
    } else {
        // 
    }
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // EXTRACT CONTENT FROM MESSAGE
    const {
        emojisAttached,
        imagesCollection,
        mentionedNameDescriptions,
        mentionsCollection,
        replies,
        scrapedUrls,
        repliedImagesCollection,
        repliedTranscription
    } = await extractContent(
        message,
        localMongo
    );
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // EXTRACT PARTICIPANTS
    const { participantsCollection } = await extractParticipants(queuedDatum, localMongo);
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // GENERATE REPLY
    const { 
        generatedText,
        imagePrompt,
        systemPrompt 
    } = await generateReply(
        queuedDatum,
        mentionsCollection,
        imagesCollection,
        emojisAttached,
        replies,
        mentionedNameDescriptions,
        scrapedUrls,
        participantsCollection,
        canGenerateImage,
        repliedImagesCollection,
        repliedTranscription,
        localMongo
    )
    generatedTextResponse = generatedText;
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // GENERATE SUMMARY
    const summary2 = await AIService.generateSummaryFromMessage(message, generatedTextResponse);
    DiscordUtilityService.setUserActivity(client, summary2);
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    // GENERATE IMAGE AND NEW REPLY
    let imageFileName;
    if (canGenerateImage) {
        try {
            const { image, text, newImagePrompt } = await generateImageAndResponse( 
                queuedDatum,
                localMongo,
                imagePrompt,
                generatedText,
                systemPrompt,
                mentionsCollection,
            );

            imageFileName = newImagePrompt;
        
            generatedImage = image;
            generatedTextResponse = text;
        } catch (error) {
            generatedImage = null;
        }
    }
    // IF NO TEXT RESPONSE WAS GENERATED, ERROR
    if (!generatedTextResponse) {
        timerInterval.unref();
        await message.reply("...");
        lastMessageSentTime = luxon.DateTime.now().toISO();
        LightWrapper.setState({ color: 'red' }, config.PRIMARY_LIGHT_ID);
        console.error(`❌ [DiscordService:replyMessage] NO RESPONSE GENERATED
${member ? `Member: ${combinedNames}` : `User: ${combinedNames}`}
${combinedGuildInformation ? `Guild: ${combinedGuildInformation}` : 'Direct Message'}
${combinedChannelInformation ? `Channel: ${combinedChannelInformation}` : ''}
${combinedGuildInformation && combinedChannelInformation ? `URL: https://discord.com/channels/${guild.id}/${channel.id}/${message.id}` : ''}`);
        return;
    }
    // SEND THE REPLY
    try {
        // This prevents the bot from trying to reply to messages that have been deleted
        // !note: Would be nice to be able to stop processing as soon as the message is deleted
        await message.fetch();
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
        await DiscordUtilityService.sendMessageInChunks('reply', message, generatedTextResponse, generatedImage, imageFileName);
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    } catch (error) {
        console.warn(`❌ [DiscordService:replyMessage] MESSAGE NOT FOUND (OR DELETED)
    ${member ? `Member: ${combinedNames}` : `User: ${combinedNames}`}
    ${combinedGuildInformation ? `Guild: ${combinedGuildInformation}` : 'Direct Message'}
    ${combinedChannelInformation ? `Channel: ${combinedChannelInformation}` : ''}
    ${combinedGuildInformation && combinedChannelInformation ? `URL: https://discord.com/channels/${guild.id}/${channel.id}/${message.id}` : ''}`);
        LightWrapper.setState({ color: 'red' }, config.PRIMARY_LIGHT_ID);
        return;
    }

    lastMessageSentTime = luxon.DateTime.now().toISO();
    timerInterval.unref();
    // LOGGING SUCCESS
    if (guild) {
        console.log(LogFormatter.replyGuildMessageSuccess(message, generatedTextResponse, timer));
    } else {
        console.log(LogFormatter.replyDirectMessageSuccess(message, generatedTextResponse, timer));
    }
    LightWrapper.setState({ color: 'white' }, config.PRIMARY_LIGHT_ID);
    return;
}

async function generateUserConversationHash(queuedDatum, recentMessage, localMongo) {
    // generate hash of user messages
    const { message, recentMessages }  = queuedDatum;
    const userMessages = recentMessages.filter(message => message.author.id === recentMessage.author.id);
    const userMessagesAsText = userMessages.map(message => message.content).join('\n\n');
    const hash = crypto.createHash('sha256').update(userMessagesAsText).digest('hex');
    // save hash to database
    const db = localMongo.db('lupos');
    const collection = db.collection('userConversations');
    const existingConversation = await collection.findOne({ hash });
    if (existingConversation) {
        // console.log('1111111111111111111111111 Found existing conversation:', existingConversation.conversation);
        return existingConversation.conversation;
    }
    // console.log('222222222222222222222 No existing conversation found, generating new one.');
    // if not, generate conversation
    const conversation = await generateUserConversation(queuedDatum, recentMessage);
    // save conversation to database
    await collection.insertOne({
        hash,
        userId: message.author.id,
        conversation,
        createdAt: new Date()
    });
    return conversation;
    // return conversation;
    // return generateUserConversation(queuedDatum, recentMessage);
}

async function generateUserConversation(queuedDatum, recentMessage) {
    const { message, recentMessages }  = queuedDatum;
    const userMessages = recentMessages.filter(message => message.author.id === recentMessage.author.id);
    const userMessagesAsText = userMessages.map(message => message.content).join('\n\n');

    const conversation = [
        {
            role: 'system',
            content: `You are an expert at providing concise, accurate descriptions of messages. Analyze the content sent to you and create a detailed summary of what ${DiscordUtilityService.getNameFromItem(recentMessage)} is discussing. Focus on being precise and direct while capturing all key points and context from their message.
            
            As the output, I want you to provide the descriptions in dash list form, without using any bold, italics, or any other formatting. You can have nested lists, but no more than 3 levels deep. Do not announce that you are generating a response, just provide the descriptions. Seperate each line with a new line, not two new lines.`
        },
        {
            role: 'user',
            name: DiscordUtilityService.getCleanUsernameFromUser(message.author),
            content: `Recent messages from ${DiscordUtilityService.getNameFromItem(recentMessage)}: ${userMessagesAsText}`,
        }
    ];
    const generatedText = await AIService.generateText({
        conversation: conversation,
        type: 'OPENAI',
        performance: 'LOW',
        tokens: config.LANGUAGE_MODEL_MAX_TOKENS,
        temperature: config.LANGUAGE_MODEL_TEMPERATURE
    });
    return generatedText;
}

async function extractParticipants(queuedDatum, localMongo, maxSimultaneous = 50) {
    const { message, recentMessages } = queuedDatum;
    const client = message.client;
    let participantsCollection = new Collection();
    let conversationsPromises = [];
    if (message.guild) {
        for (const recentMessage of recentMessages.values()) {
            const member = recentMessage.member;
            const user = recentMessage.author;

            // Add null check for user
            if (!user || !user.id) {
                console.warn(`❌ [DiscordService:getParticipants] User is null or missing ID in message: ${recentMessage.id}`);
                continue;
            }

            const botMention = DiscordUtilityService.getUserMentionFromMessage(client);
            const userMention = DiscordUtilityService.getUserMentionFromMessage(recentMessage);

            if (user.id && userMention !== botMention) {
                let userExists = participantsCollection.get(user.id);
                if (!userExists) {
                    participantsCollection.set(user.id, { user, member });
                    conversationsPromises.push(generateUserConversationHash(queuedDatum, recentMessage, localMongo).then(conv => {
                        participantsCollection.get(user.id).conversation = conv;
                    }));
                } else if (userExists.time < recentMessage.createdTimestamp) {
                    userExists.time = recentMessage.createdTimestamp;
                }
            }
        }
    }

    for (let i = 0; i < conversationsPromises.length; i += maxSimultaneous) {
        await Promise.all(conversationsPromises.slice(i, i+maxSimultaneous));
    }
    return { participantsCollection };
}

async function processMessageImages(message, generateDescriptions = false, localMongo) {
    const imagesCollection = new Collection();
    const imageUrls = [];
    
    if (!message) {
        return { imagesCollection };
    }
    
    // Helper function to add image to collection
    function addImageToCollection(imageUrl, description, hash) {
        const collectionObject = {
            imageUrl: imageUrl,
            imageDescription: description
        };

        const shortId = hash.substring(0, 8);
        
        imagesCollection.set(shortId, collectionObject);
    }
    
    // Extract URLs from message content
    const urls = message.content.match(/(https?:\/\/[^\s]+)/g);
    
    // Process message attachments
    for (const attachment of message.attachments.values()) {
        if (attachment.contentType?.includes('image')) {
            imageUrls.push(attachment.url);
        }
    }
    
    // Process URLs in message content
    if (urls?.length) {
        for (const url of urls) {
            if (!url.includes('https://tenor.com/view/')) {
                const isImage = await UtilityLibrary.isImageUrl(url);
                if (isImage) {
                    imageUrls.push(url);
                }
            } else {
                const tenorImage = await PuppeteerWrapper.scrapeTenor(url);
                imageUrls.push(tenorImage.image);
            }
        }
    }
    
    // Process collected images
    const db = localMongo.db('lupos');
    const collection = db.collection('attachmentDescriptions');
    for (let i = 0; i < imageUrls.length; i++) {
        const imageUrl = imageUrls[i];
        const { hash, fileType } = await UtilityLibrary.generateFileHash(imageUrl);
        const existingImage = await collection.findOne({ hash });

        if (!existingImage) {
            let imageDescription = null;
            if (generateDescriptions) {
                const { response } = await AIService.generateVision(imageUrl, 'Describe this image');
                if (response?.choices[0]?.message?.content) {
                    imageDescription = response.choices[0].message.content;
                    await collection.insertOne({
                        hash,
                        url: imageUrl,
                        description: imageDescription,
                        type: fileType,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                    });
                }
            }
            addImageToCollection(imageUrl, imageDescription, hash);
        } else {
            console.log('Found existing image description:', existingImage.description);
            addImageToCollection(imageUrl, existingImage.description, existingImage.hash);
        }
    }
    
    return imagesCollection;
}

async function generateUserProfileDescription(imageUrl, localMongo, imageType, userId) {
    let imageDescription;
    if (imageUrl) {
        const { hash, fileType } = await UtilityLibrary.generateFileHash(imageUrl);
        const db = localMongo.db('lupos');
        const collection = db.collection('profileDescriptions');
        const existingImage = await collection.findOne({ hash });
        if (!existingImage) {       
            const { response, error } = await AIService.generateVision(imageUrl, 'Describe this image');
            if (response?.choices[0].message.content) {
                imageDescription = response?.choices[0].message.content;
                // Delete any existing descriptions for this user and image type
                await collection.deleteMany({ userId: userId, imageType: imageType });
                // Insert the new description
                await collection.insertOne({
                    hash,
                    url: imageUrl,
                    description: imageDescription,
                    userId: userId,
                    imageType: imageType,
                    fileType: fileType,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                });
            }
        } else {
            imageDescription = existingImage.description;
        }
    }
    return imageDescription;
}

async function generateRolesEmbedMessage(client) {
    // get the original message and edit it to show the new role count on the button
    // re-render the buttons with the new role count
    const classesEmbed = new EmbedBuilder()
        .setTitle('Pick Your WoW Classes')
        .setDescription('Which classes do you play as?')
        .setColor('#00FF00')

    const roles = client.guilds.cache.get(config.GUILD_ID_WHITEMANE).roles.cache
        .sort((a, b) => a.rawPosition - b.rawPosition)
        .reverse();

    const filteredClasses = roles.filter(role => warcraftClasses.some(videogameRole => videogameRole.id === role.id));
    const classesToArray = filteredClasses.map(role => role);

    const classesRows = [];
    const maxButtonsPerRow = 5;
    for (let i = 0; i < classesToArray.length; i += maxButtonsPerRow) {
        const row = new ActionRowBuilder();
        const currentRoles = classesToArray.slice(i, i + maxButtonsPerRow); 
        for (const role of currentRoles) {
            const emoji = warcraftClasses.find(videogameRole => videogameRole.id === role.id)?.emojiId || null;
            const button = new ButtonBuilder()
                .setLabel(`${role.name} (${role.members.size})`)
                .setStyle(ButtonStyle.Secondary)
                .setCustomId(`pick-role-${role.id}`)
                // emoji is in warcraftClasses as emojiId
                .setEmoji(warcraftClasses.find(warcraftClass => warcraftClass.id === role.id)?.emojiId || null);
            row.addComponents(button);
        }
        classesRows.push(row);
    }

    const factionEmbed = new EmbedBuilder()
        .setTitle('Pick Your WoW Faction')
        .setDescription('Which faction do you play as?')
        .setColor('#00FF00')
        
    const filteredFactions = roles.filter(role => warcraftFactions.some(videogameRole => videogameRole.id === role.id));
    const factionsToArray = filteredFactions.map(role => role);

    const factionsRows = [];
    for (let i = 0; i < factionsToArray.length; i += maxButtonsPerRow) {
        const row = new ActionRowBuilder();
        const currentRoles = factionsToArray.slice(i, i + maxButtonsPerRow);
        for (const role of currentRoles) {
            const button = new ButtonBuilder()
                .setLabel(`${role.name} (${role.members.size})`)
                .setStyle(ButtonStyle.Secondary)
                .setCustomId(`pick-role-${role.id}`)
                .setEmoji(warcraftFactions.find(warcraftFaction => warcraftFaction.id === role.id)?.emojiId || null);
            row.addComponents(button);
        }
        factionsRows.push(row);
    }

    const videogamesEmbed = new EmbedBuilder()
        .setTitle('Pick Your Videogames')
        .setDescription('Which videogames do you play?')
        .setColor('#00FF00');

    const filteredVideogames = roles.filter(role => rolesVideogames.some(videogameRole => videogameRole.id === role.id));
    const videogamesToArray = filteredVideogames.map(role => role);

    const videogamesRows = [];
    for (let i = 0; i < videogamesToArray.length; i += maxButtonsPerRow) {
        const row = new ActionRowBuilder();
        const sortedVideogames = videogamesToArray.sort((a, b) => a.name.localeCompare(b.name));
        const currentRoles = sortedVideogames.slice(i, i + maxButtonsPerRow);
        for (const role of currentRoles) {
            const emoji = rolesVideogames.find(videogameRole => videogameRole.id === role.id)?.emojiId || null;
            const button = new ButtonBuilder()
                .setLabel(`${role.name} (${role.members.size})`)
                .setStyle(ButtonStyle.Secondary)
                .setCustomId(`pick-role-${role.id}`);
            if (emoji) {
                button.setEmoji(emoji);
            }
            row.addComponents(button);
        }
        videogamesRows.push(row);
    }

    // if the channel is empty, create a new message
    const channel = DiscordUtilityService.getChannelById(client, config.WHITEMANE_SELF_ROLES_CHANNEL_ID);
    if (!channel) {
        return;
    }
    const messagesCacheSize = channel.messages.cache.size || await channel.messages.fetch({ limit: 10 }).then(messages => messages.size);
    // await channel.bulkDelete(100);
    // if the channel is empty, post message, otherwise edit the first message
    if (messagesCacheSize === 0) {
        await channel.send({ embeds: [factionEmbed], components: factionsRows });
        await channel.send({ embeds: [classesEmbed], components: classesRows });
        await channel.send({ embeds: [videogamesEmbed], components: videogamesRows });

        const guildMastersEmbed = new EmbedBuilder()
            .setTitle('Guild Masters / Officers')
            .setDescription(`If you would like access to post in our guild recruitment channel and other guild leadership channels, please post on the <#966457267417411614> channel:

- Include a screenshot of your guild tab showing you as GM or officer, as well as the name and faction of the guild.
- Put your guild tag in your Discord nickname <Like This>.
            `)
            .setColor('#00FF00')

        await channel.send({ embeds: [guildMastersEmbed] });

        return;
    } else {
        const allMessages = await channel.messages.fetch({ limit: 20 });
        const message1 = allMessages.at(allMessages.size - 1);
        const message2 = allMessages.at(allMessages.size - 2);
        const message3 = allMessages.at(allMessages.size - 3);
        await message1.edit({ embeds: [factionEmbed], components: factionsRows });
        await message2.edit({ embeds: [classesEmbed], components: classesRows });
        await message3.edit({ embeds: [videogamesEmbed], components: videogamesRows });
        return;
    }
}

async function extractContent(message, localMongo) {
    const customDescriptions = MessageConstant.customContextWhitemane;
    const mentionedNameDescriptions = [];
    const mentionsCollection = new Collection();
    let userIdsInMessage = [];
    const replies = [];

    // Remove first self mention from message
    const messageHasMentions = message.content.match(/<@!?\d+>/g) || [];
    const messageHasSelfMention = message.content.match(/\b(me|i)\b/i) && message.author;
    const messageHasYourself = message.content.match(/\byourself\b/i) && message.author;
    const messageHasAndYou = message.content.match(/\band\s+you\b/i) && message.author;
    const messageHasWithYou = message.content.match(/\bwith\s+you\b/i) && message.author;
    const messageHasUs = message.content.match(/\bus\b/i) && message.author;

    const repliedMessage = message.reference ? await message.channel.messages.fetch(message.reference.messageId) : null;
    const repliedMessageHasMentions = repliedMessage?.content.match(/<@!?\d+>/g) || [];
    const repliedMessageHasSelfMention = repliedMessage?.content.match(/\b(me|i)\b/i) && repliedMessage.author;


    const mentions = [...messageHasMentions, ...repliedMessageHasMentions];

    const clientMentionedOnce = mentions.filter(mention => mention === `<@${message.client.user.id}>`).length === 1;

    const commonWords = ["the", "be", "to", "of", "and", "a", "in", "that", "have", "I", "it", "for", "not", "on", "with", "he", "as", "you", "do", "at", "this", "but", "his", "by", "from", "they", "we", "say", "her", "she", "or", "an", "will", "my", "one", "all", "would", "there", "their", "what", "so", "up", "out", "if", "about", "who", "get", "which", "go", "me", "when", "make", "can", "like", "time", "no", "just", "him", "know", "take", "people", "into", "year", "your", "good", "some", "could", "them", "see", "other", "than", "then", "now", "look", "only", "come", "its", "over", "think", "also", "back", "after", "use", "two", "how", "our", "work", "first", "well", "way", "even", "new", "want", "because", "any", "these", "give", "day", "most", "us", "is", "am", "are", "has", "was", "were", "being", "been", "have", "do", "does", "did", "doing", "a", "an", "the", "and", "but", "if", "or", "because", "as", "until", "while", "of", "at", "by", "for", "with", "about", "against", "between", "into", "through", "during", "before", "after", "above", "below", "to", "from", "up", "down", "in", "out", "on", "off", "over", "under", "again", "further", "then", "once", "here", "there", "when", "where", "why", "how", "all", "any", "both", "each", "few", "more", "most", "other", "some", "such", "no", "nor", "not", "only", "own", "same", "so", "than", "too", "very", "s", "t", "can", "will", "just", "don", "should", "now", "as", "that", "this", "these", "those", "myself", "ourselves", "you", "yourself", "yourselves", "himself", "herself", "itself", "themselves", "what", "which", "who", "whom", "this", "that", "these", "those", "are", "was", "were", "be", "been", "being", "have", "has", "had", "having", "do", "does", "did", "doing", "will", "would", "should", "can", "could", "ought", "i", "you", "he", "she", "it", "we", "they", "me", "you", "him", "her", "it", "us", "them", "my", "your", "his", "her", "its", "our", "their", "mine", "yours", "his", "hers", "ours", "theirs", "my", "your", "his", "her", "its", "our", "their", "and", "because", "but", "or", "for", "so", "like"];

    if (repliedMessage) {
        // Get all matches first
        const mentions = repliedMessage.content.match(/<@!?\d+>/g) || [];

        // Replace each mention sequentially
        let modifiedContent = repliedMessage.content;
        for (const match of mentions) {
            const id = match.replace(/<@!?/, '').replace('>', '');
            const user = await DiscordUtilityService.getUserFromClientAndId(message.client, id);
            modifiedContent = modifiedContent.replace(match, user);
        }
        repliedMessage.content = modifiedContent;

        const reply = {
            userId: repliedMessage.author.id,
            name: DiscordUtilityService.getNameFromUser(repliedMessage.author),
            content: repliedMessage.content,
        }
        replies.push(reply);
    }

    if (mentions?.length) {
        userIdsInMessage.push(...mentions.map(user => user.replace(/<@!?/, '').replace('>', '')));
    }
    if (clientMentionedOnce) {
        userIdsInMessage = userIdsInMessage.filter(userId => userId !== message.client.user.id);
    }

    if (messageHasSelfMention || messageHasUs) {
        userIdsInMessage.push(message.author.id);
    }

    if (repliedMessageHasSelfMention) {
        userIdsInMessage.push(repliedMessage.author.id);
    }

    if (messageHasYourself || messageHasAndYou || messageHasWithYou || messageHasUs) {
        userIdsInMessage.push(message.client.user.id);
    }

    // remove duplicates
    userIdsInMessage = [...new Set(userIdsInMessage)];
    
    // Descriptions for each mentioned name
    if (userIdsInMessage.length) {
        await Promise.all(userIdsInMessage.map(async (userId) => {
            // Run user and member fetching in parallel
            const [user, member] = await Promise.all([
                DiscordUtilityService.getUserFromClientAndId(message.client, userId),
                DiscordUtilityService.getMemberFromMessageAndId(message, userId)
            ]);

            if (user) {
                const avatarUrl = user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.jpg?size=512` : '';
                
                // Run avatar description and banner ID fetching in parallel
                const [avatarDescription, bannerId] = await Promise.all([
                    generateUserProfileDescription(avatarUrl, localMongo, 'avatar', user.id),
                    DiscordUtilityService.getBannerFromUserId(message.client, user.id)
                ]);

                // Generate banner description after we have the banner ID
                const bannerUrl = bannerId ? `https://cdn.discordapp.com/banners/${user.id}/${bannerId}.jpg?size=512` : '';
                const bannerDescription = await generateUserProfileDescription(bannerUrl, localMongo, 'banner', user.id);

                const mentionExists = mentionsCollection.has(`${user.id}`);
                if (!mentionExists) {
                    mentionsCollection.set(`${user.id}`, { user, member, avatarDescription, bannerDescription });
                }
            }
        }));
    }

    const individualWords = message.content.toLowerCase().split(' ');
    for (const word of individualWords) {
        // if word starts with '<@' and ends with '>' remove, and check if it is a user
        if (word.startsWith('<@') && word.endsWith('>')) {
            let discordUsername;
            if (word !== `<@${message.client.user.id}>`) {
                discordUsername = DiscordUtilityService.getNameFromUser(message.client.users.cache.get(word.replace(/<@!?/, '').replace('>', '')));
            }
            if (discordUsername) {
                const pattern = new RegExp(`\\b${discordUsername}\\b`, 'i');
                const filteredDescriptions = customDescriptions.filter(description => pattern.test(description.keywords));
                if (filteredDescriptions.length >= 1) {
                    filteredDescriptions.forEach((description) => {
                        mentionedNameDescriptions.push( {word: discordUsername, description: description.description} );
                    });
                }
            }
        } else {
            // remove all special characters from word
            const cleanedWord = word.replace(/[^a-zA-Z0-9]/g, '');
            if (cleanedWord && !commonWords.includes(cleanedWord)) {
                const pattern = new RegExp('\\b' + cleanedWord + '\\b', 'i');
                const filteredDescriptions = customDescriptions.filter(description => pattern.test(description.keywords));
                if (filteredDescriptions.length >= 1) {
                    filteredDescriptions.forEach((description) => {
                        mentionedNameDescriptions.push( {word: cleanedWord, description: description.description} );
                    });
                }
            }
        }
    }    

    const imagesCollection = await processMessageImages(message, true, localMongo);
    const repliedImagesCollection = await processMessageImages(repliedMessage, true, localMongo);
    const repliedTranscription = '';
    
    const emojisAttached = extractEmojis(message);

    // Process URLs in message
    const urls = message.content.match(/(https?:\/\/[^\s]+)/g);
    let scrapedUrls;
    if (urls) {
        scrapedUrls = await PuppeteerWrapper.scrapeURL(urls[0]);
    }

    return {
        emojisAttached,
        imagesCollection,
        mentionedNameDescriptions,
        mentionsCollection,
        replies,
        scrapedUrls,
        repliedImagesCollection,
        repliedTranscription
    };
}

async function luposOnReady(client, { mongo, localMongo }) {
    console.log(LogFormatter.botReady(client));

    await generateRolesEmbedMessage(client);
    try {
        await mongo.connect();
        console.log(`💡 [DiscordService:luposOnReady] MONGODB CONNECTION SUCCESS`);
    } catch (error) {
        console.error(`❌ [DiscordService:luposOnReady] MONGODB CONNECTION ERROR`);
    }
    try {
        await localMongo.connect();
        console.log(`💡 [DiscordService:luposOnReady] LOCAL MONGODB CONNECTION SUCCESS`);
    } catch (error) {
        console.error(`❌ [DiscordService:luposOnReady] LOCAL MONGODB CONNECTION ERROR`);
    }
    client.user.setActivity(`Don't @ me...`, { type: ActivityType.Custom });
    // Jobs
    BirthdayJob.startJob(client);
    ChatterJob.startJob(client, mongo);
    RemindersJob.startJob(client, mongo);
    // RandomMessageJob.startJob(client);
    ActivityRoleAssignmentJob.startJob(
        client, mongo, config.WHITEMANE_POLITICS_CHANNEL_ID, config.WHITEMANE_YAPPER_ROLE_ID,
        config.WHITEMANE_OVERREACTOR_ROLE_ID, 60, 1
    );
    EventReactJob.startJob(client, mongo);
    PermanentTimeOutJob.startJob(client, mongo);

    // Check the last 100 messages in the channel politics, and if there is a message that mentions me that I haven't replied to in the last 5 minutes, reply to it
    const politicsChannel = DiscordUtilityService.getChannelById(client, config.WHITEMANE_POLITICS_CHANNEL_ID);
    // console.log('Politics channel found:', !!politicsChannel);

    if (politicsChannel) {
        // const messages = await politicsChannel.messages.fetch({ limit: 100 });
        const messages = (await DiscordUtilityService.fetchMessages(client, config.WHITEMANE_POLITICS_CHANNEL_ID, 100)).reverse();
        // console.log('Total messages fetched:', messages.size);
        
        const now = Date.now();
        
        // Convert to array and sort by timestamp (newest first)
        const messageArray = Array.from(messages.values()).sort((a, b) => b.createdTimestamp - a.createdTimestamp);
        
        // Find messages from the bot that are replies
        const botReplies = messageArray.filter(m => 
            m.author.id === client.user.id && 
            m.reference?.messageId
        );
        const repliedToIds = new Set(botReplies.map(m => m.reference.messageId));
        // console.log('Bot replies found:', botReplies.length);
        // console.log('Replied to IDs:', Array.from(repliedToIds));
        
        let mentionsFound = 0;
        for (const message of messageArray) {
            if (message.mentions.has(client.user.id) && !message.author.bot) {
                mentionsFound++;
                const timeDifference = (now - message.createdTimestamp) / 1000 / 60; // Convert to minutes
                // console.log(`Mention ${mentionsFound}: ${message.id}, Time diff: ${timeDifference.toFixed(2)} minutes, Already replied: ${repliedToIds.has(message.id)}`);
                
                if (timeDifference <= 200 && !repliedToIds.has(message.id)) {
                    UtilityLibrary.consoleLog('=', `Found message in politics channel that mentions me and I haven't replied to in the last 5 minutes: ${message.content}`);
                    await processMessage(client, { mongo, localMongo }, message);
                    break;
                }
            }
        }
        // console.log('Total mentions found:', mentionsFound);
    }
}

async function luposOnReadyReports(client, mongo) {
    UtilityLibrary.consoleLog('<')
    UtilityLibrary.consoleLog('=', `Logged in as ${DiscordUtilityService.getBotName(client)}`)
    try {
        await mongo.connect();
        UtilityLibrary.consoleLog('=', 'Connected to MongoDB')
    } catch (error) {
        UtilityLibrary.consoleLog('=', `Error connecting to MongoDB \n${error}`)
    }
    // DiscordUtilityService.printOutAllRoles(client);
    // DiscordUtilityService.printOutAllEmojis(client);
    // DiscordUtilityService.displayAllChannelActivity(client, mongo)
    await DiscordUtilityService.calculateMessagesSentOnAveragePerDayInChannel(client, config.WHITEMANE_POLITICS_CHANNEL_ID);
    UtilityLibrary.consoleLog('>');
}

async function processMessage(client, { mongo, localMongo }, message) {
    const isDirectMessageFromBot = message.channel.type === ChannelType.DM && message.author.id === client.user.id;
    const isMessageWithoutBotMention = message.channel.type != ChannelType.DM && !message.mentions.has(client.user);
    const isMessageFromBot = message.author.bot;

    const isGuildWhitemane = message?.guildId === config.GUILD_ID_WHITEMANE;

    // if the message is from this specific channel ids: '123' or from this guild id: '456'
    if (message.channelId === '1389725795869786284' || (message.guildId === '1357934319879979170' && message.channelId === '1392947449513119776') || message.channelId === '1393700608967708672') {
        // console.log('entered');
        await YouTubeWrapper.searchAndPlay(client, message);
        await YouTubeWrapper.stop(client, message);
        await YouTubeWrapper.next(client, message);
        await YouTubeWrapper.pause(client, message);
        await YouTubeWrapper.resume(client, message);
        await YouTubeWrapper.setVolume(client, message);
        // await DiscordVoiceService.recordVoiceInVoiceChannel2(client, message);
        // return;
    }

    // if it's not in this channel: '835237008691560528' or this channel: '835237008691560528'
    if (message.channelId !== '835237008691560528' && message.channelId !== '835237008691560528') {
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    }

    if (isMessageWithoutBotMention) {
        return;
    }

    // IGNORE MESSAGES FROM BOT ACCOUNTS
    if (isMessageFromBot) {
        return;
    }

    // ASSIGN ROLES TO USERS BASED ON CHANNELS
    for (const channel of channels) {
        if (message.channelId === channel.id) {
            await DiscordUtilityService.addRoleToMember(client, message.author.id, channel.roleId);
        }
    }

    // IGNORE MESSAGES FROM THE BOT ITSELF
    if (isDirectMessageFromBot) {
        return;
    }

    // IGNORE MESSAGES FROM SPECIFIC USERS
    if (
        message.author.id === '1387618309071048745' //smokeyfacesplitter666
        || message.author.id === '679626713310691358'
        || message.author.id === '274558094896529408'
    ) {
        return;
    }

    // IGNORE MESSAGES FROM USERS WITH SPECIFIC ROLES
    if (
        message.member &&
        message.member.roles.cache.has('1394148043153997935')
    ) {
        return;
    }

    // START TYPING
    if (!typingIntervals[message.channel.id]) {
        typingIntervals[message.channel.id] = await DiscordUtilityService.startTypingInterval(message.channel);
    }

    // LUPOS CHATTER ROLE
    if (isGuildWhitemane) {
        const chatterRoleId = '1353101921681936456';
        await DiscordUtilityService.addRoleToMember(client, message.author.id, chatterRoleId);

        // remove after 5 minutes
        setTimeout(async () => {
            await DiscordUtilityService.removeRoleFromUser(client, message.author.id, chatterRoleId);
        }, 10000);
    }
    

    let recentMessages = (await DiscordUtilityService.fetchMessages(client, message.channel.id, 500)).reverse()
    // let recentMessages = fetchRecentMessages.map((msg) => msg);

    queuedData.push({message, recentMessages});

    if (!isProcessingQueue) {
        isProcessingQueue = true;
        while (queuedData.length > 0) {
            const queuedDatum = queuedData.shift();
            await replyMessage(queuedDatum, localMongo);
            // check if queue has more messages in the same channel, if not, stop typing
            if (queuedData.length === 0 || !queuedData.some(q => q.message.channel.id === message.channel.id)) {
                DiscordUtilityService.clearTypingInterval(typingIntervals[message.channel.id]);
                delete typingIntervals[message.channel.id];
            }
        }
        isProcessingQueue = false;
        typingIntervals = {};
        return
    }
}

async function luposOnMessageCreate(client, { mongo, localMongo}, message) {
    await processMessage(client, { mongo, localMongo}, message);
}

async function luposOnMessageUpdate(client, { mongo, localMongo}, oldMessage, newMessage) {
    // IF UPDATED MESSAGE MENTIONS ME, PROCESS IT
    if (newMessage.mentions.has(client.user) && !oldMessage.mentions.has(client.user)) {
        await processMessage(client, { mongo, localMongo}, newMessage);
    } else {
        return;
    }
}

async function processCreateReaction(client, queuedReaction) {
    const { reaction, user } = queuedReaction;
    const messageId = reaction.message.id;
    const userId = reaction.message.author?.id;
    const guildId = reaction.message.guildId;
    const channelId = reaction.message.channelId;
    const channelName = DiscordUtilityService.getChannelName(client, channelId);
    const uniqueUserLengthTrigger = 5;
    const highlightsChannel = config.CHANNEL_ID_WHITEMANE_HIGHLIGHTS;
    const content = reaction.message.content;

    if (channelId === config.CHANNEL_ID_WHITEMANE_HIGHLIGHTS || channelId === config.CHANNEL_ID_WHITEMANE_BOOTY_BAE) return;


    if (!allUniqueUsers[messageId]) {
        allUniqueUsers[messageId] = new Set();
    } else {
        allUniqueUsers[messageId].add(userId);
    }

    const users = await reaction.users.fetch();
    users.map(user => allUniqueUsers[messageId].add(user.id));
    console.log(LogFormatter.reactionAdded(user, reaction));
    
    if ([...allUniqueUsers[messageId]].length >= uniqueUserLengthTrigger) {
        const attachments = reaction.message.attachments;
        const stickers = reaction.message.stickers;
        const name = DiscordUtilityService.getDisplayNameFromUserOrMember({ member: reaction.message.member, user: reaction.message.author });
        const avatar = reaction.message.author?.avatar;
        const avatarUrl = avatar ? `https://cdn.discordapp.com/avatars/${userId}/${avatar}.jpg?size=512` : '';

        const emojiId = reaction._emoji.id
        const emojiName = reaction._emoji.name;
        const isEmojiAnimated = reaction._emoji.animated;
        let emojiUrl;

        const doesContentContainTenorText = content?.includes('https://tenor.com/view/')

        if (!name) return;

        if (emojiId && isEmojiAnimated) {
            emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.gif`;
        } else if (emojiId && !isEmojiAnimated) {
            emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.png`;
        }


        const banner = reaction.message.author?.banner;
        const reference = reaction.message.reference;
        const referenceChannelId = reference?.channelId;
        const referenceGuildId = reference?.guildId;
        const referenceMessageId = reference?.messageId;
        let referenceMessage;


        const currentReferenceChannel = DiscordUtilityService.getChannelById(client, referenceChannelId);

        if (currentReferenceChannel?.messages) {
            referenceMessage = await currentReferenceChannel.messages.fetch(referenceMessageId)
        }

        const targetChannel = DiscordUtilityService.getChannelById(client, highlightsChannel);

        const messageURL = `https://discord.com/channels/${guildId}/${channelId}/${messageId}`;

        const embed = new EmbedBuilder()
            .setTitle(`#${channelName}`)
            .setURL(messageURL)
            // .addFields(
            //     { name: 'Regular field title', value: 'Some value here' },
            //     { name: '\u200B', value: '\u200B' },
            //     { name: 'Inline field title', value: 'Some value here', inline: true },
            //     { name: 'Inline field title', value: 'Some value here', inline: true },
            // )
            // .addFields({ name: 'Inline field title', value: 'Some value here', inline: true })

        if (referenceMessage) {
            const referenceAttachments = referenceMessage.attachments;
            const referenceStickers = referenceMessage.stickers;
            if (referenceMessage.content) {
                embed.addFields({ name: 'Replying To', value: referenceMessage.content });
            }
            if (referenceAttachments) {
                for (const attachment of referenceAttachments.values()) {
                    embed.setImage(attachment.url);
                }
            }
            if (referenceStickers) {
                for (const sticker of referenceStickers.values()) {
                    embed.setImage(sticker.url);
                }
            }
        }

        const totalReactions = [...allUniqueUsers[messageId]].length > reaction.message.reactions.cache.size ? [...allUniqueUsers[messageId]].length : reaction.message.reactions.cache.size;

        const emojiResponse = '<:emoji:1111811553491169280>';
        
        embed.addFields({ name: 'Reactions', value: `${emojiId ? '❤️' : emojiName } ${totalReactions}` });

        if (emojiUrl) {
            embed.setThumbnail(emojiUrl);
        }

        if (avatarUrl) {
            embed.setAuthor({ name: name, iconURL: avatarUrl, url: messageURL })
        } else {
            embed.setAuthor({ name: name, url: messageURL })
        }

        if (content) {
            embed.setDescription(content);
        }

        if (doesContentContainTenorText) {
            let regex = /(https:\/\/tenor\.com\/view\/\S*)/;
            let match = content.match(regex);
            let url = match ? match[0] : '';
            const tenorImage = await PuppeteerWrapper.scrapeTenor(url);
            embed.setImage(tenorImage.image);
        }

        if (attachments) {
            for (const attachment of attachments.values()) {
                embed.setImage(attachment.url);
            }
        }

        if (stickers) {
            for (const sticker of stickers.values()) {
                embed.setImage(sticker.url);
            }
        }

        embed.setTimestamp(new Date(reaction.message.createdTimestamp));
        embed.setFooter({ text: messageId, iconURL: 'https://cdn.discordapp.com/icons/609471635308937237/cfeccc9c5372c8ae8130b184fd1c5346.png?size=256' })

        if (!reactionMessages[messageId]) {
            const message = await targetChannel.send({ embeds: [embed] });
            reactionMessages[messageId] = message.id;
        } else {
            const message = await targetChannel.messages.fetch(reactionMessages[messageId]);
            await message.edit({ embeds: [embed] });
        }
    }
}

async function luposOnReactionCreateQueue(client, mongo, reaction, user) {
    if (reaction.message.guild.id !== config.GUILD_ID_WHITEMANE) return;
    
    await EventReactJob.processJob(client, mongo, reaction, user);
    const isHighlightChannel = reaction.message.channelId === config.CHANNEL_ID_WHITEMANE_HIGHLIGHTS;
    const isNSFWChannel = reaction.message.channelId === config.CHANNEL_ID_WHITEMANE_BOOTY_BAE;
    if (isHighlightChannel) return;
    if (isNSFWChannel) return;

    reactionQueue.push({ reaction, user });

    if (!isProcessingOnReactionQueue) {
        isProcessingOnReactionQueue = true;
        while (reactionQueue.length > 0) {
            const queuedReaction = reactionQueue.shift();
            await processCreateReaction(client, queuedReaction);
        }
        isProcessingOnReactionQueue = false;
        return
    }
}

// Whenever a new member joins the server
async function luposOnGuildMemberAdd(client, mongo, member) {
    if (member.guild.id !== config.GUILD_ID_WHITEMANE) return;
    const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member });
    const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(member.guild);
    console.log(LogFormatter.memberJoinedGuild(member));
}

// Whenever a member is updated
async function luposOnGuildMemberUpdate(client, mongo, oldMember, newMember) {
    if (oldMember.guild.id !== config.GUILD_ID_WHITEMANE) return;
    // Whenever a user completes onboarding
    const hasOldMemberCompletedOnboarding = oldMember.flags & 1 << 1;
    const hasNewMemberCompletedOnboarding = newMember.flags & 1 << 1;
    if (!hasOldMemberCompletedOnboarding && hasNewMemberCompletedOnboarding) {
        LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
        console.log(LogFormatter.memberUpdateOnboardingComplete(newMember));
        await generateRolesEmbedMessage(client);
    }
}

async function luposOnInteractionCreate(client, mongo, interaction) {
    const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member: interaction.member, user: interaction.user });
    const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(interaction.guild);
    const combinedChannelInformation = DiscordUtilityService.getCombinedChannelInformationFromChannel(interaction.channel);
    console.log(LogFormatter.interactionCreate(interaction));
    if (interaction.isButton()) {
        console.log(LogFormatter.interactionCreateButton(interaction));
        if (interaction.customId.startsWith('pick-role-')) {
            const roleId = interaction.customId.split('pick-role-')[1];
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                console.error(clRoleNotFound(interaction, roleId));
                return;
            }
            const member = interaction.guild.members.cache.get(interaction.user.id);
            if (!member) {
                console.error(clMemberNotFound(interaction, roleId));
                return;
            }
            if (member.roles.cache.has(roleId)) {
                console.log(LogFormatter.roleRemoved(interaction, role));
                await interaction.reply({ content: `Removing <@&${roleId}>...`, flags: MessageFlags.Ephemeral });
                await member.roles.remove(role);
                // update reply message to say role removed
                await interaction.editReply({ content: `Removed <@&${roleId}>!`, flags: MessageFlags.Ephemeral });
                // wait 5 seconds before deleting the reply
                await new Promise(resolve => setTimeout(resolve, 5000));
                await interaction.deleteReply();
                await generateRolesEmbedMessage(client, interaction);
                return;
            } else {
                console.log(LogFormatter.roleAdded(interaction, role));
                await interaction.reply({ content: `Adding <@&${roleId}>...`, flags: MessageFlags.Ephemeral });
                await member.roles.add(role);
                // update reply message to say role added
                await interaction.editReply({ content: `Added <@&${roleId}>!`, flags: MessageFlags.Ephemeral });
                // wait 5 seconds before deleting the reply
                await new Promise(resolve => setTimeout(resolve, 5000));
                await interaction.deleteReply();
                await generateRolesEmbedMessage(client, interaction);
                return;
            }
        }

        if (interaction.customId === 'volumeUp') {
            const reply = await interaction.deferReply();
            YouTubeWrapper.setVolumeByAmount(5);
            await reply.delete();
            return;
        } else if (interaction.customId === 'volumeDown') {
            const reply = await interaction.deferReply();
            YouTubeWrapper.setVolumeByAmount(-5);
            await reply.delete();
            return;
        } else if (interaction.customId === 'pause') {
            const reply = await interaction.deferReply();
            YouTubeWrapper.buttonPause();
            await reply.delete();
            return;
        } else if (interaction.customId === 'resume') {
            const reply = await interaction.deferReply();
            YouTubeWrapper.buttonResume();
            await reply.delete();
            return;
        } else if (interaction.customId === 'next') {
            const reply = await interaction.deferReply();
            YouTubeWrapper.buttonNext();
            await reply.delete();
            return;
        }
    } else if (interaction.isCommand()) {
        // UtilityLibrary.consoleLog('=', `Command interaction received: ${interaction.commandName}`);
        console.log(LogFormatter.interactionCreateCommand(interaction));
        if (interaction.commandName === 'ping') {
            await interaction.reply('Pong!');
            return;
        }
        // if (interaction.commandName === 'recordvoice') {
        //     return;
        // }
        else {
            const command = client.commands.get(interaction.commandName);

            if (!command) {
                console.error(clCommandNotFound(interaction));
                return;
            }
        
            try {
                await command.execute(interaction);
            } catch (error) {
                // console.error('123123123', error);
                if (interaction.replied || interaction.deferred) {
                    // await interaction.followUp({ content: 'There was an error while executing this command!', ephemeral: true });
                    // do nothing
                    return;
                } else {
                    console.log(LogFormatter.commandError(interaction, error));
                    await interaction.reply({ content: 'There was an error while executing this command!', ephemeral: true });
                }
            }
        }
    }
}

async function luposOnPresenceUpdate(client, mongo, oldPresence, newPresence) {
    if (newPresence.guild.id !== config.GUILD_ID_WHITEMANE) return;

    try {
        
        let activityName = '';
        let isStreaming = false;
        let userName = newPresence.user.username;
        let streamingUrl = '';
        let userStatus = newPresence.status;
        
        // Check activities
        for (const activity of newPresence.activities) {
            // playing
            if (activity.type === 0) {
                activityName = activity.name;
                // console.log(`${newPresence.user.tag} is playing ${activity.name}.`);
                // output activity name to a file, game_activity_log.txt, which is one directory up from the current directory
                // const file = path.join(__dirname, '../game_activity_log.txt');
                // fs.appendFileSync(file, `${activity.name}\n`);
                // only add unique game names, but count how many times the game has been played
                
                const db = mongo.db('lupos');
                const collection = db.collection('game_activity');
                const existingActivity = await collection.findOne({ name: activity.name });
                if (!existingActivity) {
                    await collection.insertOne({ name: activity.name, count: 1 });
                } else {
                    await collection.updateOne({ name: activity.name }, { $inc: { count: 1 } });
                }

                // const existingContent = fs.existsSync(file) ? fs.readFileSync(file, 'utf8') : '';
                // if (!existingContent.includes(activity.name)) {
                //     fs.appendFileSync(file, `${activity.name}\n`);
                // }
                if (activity.name.toLowerCase().includes('apex legends')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'apex legends')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('ashes of creation')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'ashes of creation')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('counter-strike')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'counter-strike')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('deadlock')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'deadlock')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('diablo')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'diablo')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('dota')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'dota 2')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('fortnite')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'fortnite')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('league of legends')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'league of legends')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('marvel rivals')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'marvel rivals')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('minecraft')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'minecraft')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('overwatch')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'overwatch')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('runelite')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'runescape')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('the sims')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'the sims')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('valorant')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'valorant')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
                if (activity.name.toLowerCase().includes('warhammer')) {
                    const roleId = rolesVideogames.find(role => role.name.toLowerCase() === 'warhammer')?.id;
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                }
            }
            // streaming
            if (activity.type === 1) {
                isStreaming = true;
                streamingUrl = activity.url;
                // console.log(`${newPresence.user.tag} is streaming ${activity.name} at ${activity.url}.`);
            }
            // listening
            if (activity.type === 2) {
                activityName = activity.name;
                const roleId = '1392632930957918239';
                await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, roleId);
                // console.log(`${newPresence.user.tag} is listening to ${activity.name}.`);
            }
            // watching
            if (activity.type === 3) {
                activityName = activity.name;
                // console.log(`${newPresence.user.tag} is watching ${activity.name}.`);
            }
            // custom
            if (activity.type === 4) {
                activityName = activity.name;
                // console.log(`${newPresence.user.tag} has a custom status: ${activity.name}.`);
            }
            // competing
            if (activity.type === 5) {
                activityName = activity.name;
                // console.log(`${newPresence.user.tag} is competing in ${activity.name}.`);
            }
        }
        
        if (isStreaming) {
            const threeHoursAgo = new Date(Date.now() - (3 * 60 * 60 * 1000));
            const db = mongo.db('lupos');
            const streamersCollection = db.collection('streamers');
            
            // Find and update or insert
            const result = await streamersCollection.findOneAndUpdate(
                { userId: newPresence.user.id },
                {
                    $set: {
                        userId: newPresence.user.id,
                        userName: userName,
                        streamingUrl: streamingUrl,
                        activityName: activityName,
                        isStreaming: isStreaming,
                        timestamp: new Date()
                    }
                },
                {
                    upsert: true,
                    returnDocument: 'before' // Returns the document before update (or null if inserted)
                }
            );
            
            // Check if we should notify (no previous record or last notification was more than 3 hours ago)
            const shouldNotify = !result || new Date(result.timestamp) < threeHoursAgo;
            
            if (shouldNotify) {
                try {
                    // Scrape metadata from Twitch
                    const metadata = await PuppeteerWrapper.scrapeTwitchUrl(streamingUrl);
                    
                    // Assign streamer role to user
                    const streamerRoleId = '1392951318951231629';
                    await DiscordUtilityService.addRoleToMember(client, newPresence.user.id, streamerRoleId);
                    
                    // Get the streaming channel
                    const streamingChannel = await DiscordUtilityService.getChannelById(client, config.WHITEMANE_STREAMERS_CHANNEL_ID);
                    
                    if (streamingChannel) {
                        const userTag = `<@${newPresence.user.id}>`;
                        
                        // Create embed
                        const embed = new EmbedBuilder()
                            .setAuthor({ 
                                name: `${userName} is now live on Twitch!`, 
                                iconURL: newPresence.user.displayAvatarURL() 
                            })
                            .setURL(streamingUrl)
                            .setDescription(`${userTag} is streaming **${activityName}**`)
                            .setColor('#57F287')
                            .setTimestamp();
                        
                        // Add thumbnail if available
                        if (metadata?.image) {
                            embed.setThumbnail(metadata.image);
                        }
                        
                        // Add title from description (max 256 characters)
                        if (metadata?.description) {
                            const title = metadata.description.length > 256 
                                ? metadata.description.substring(0, 253) + '...' 
                                : metadata.description;
                            embed.setTitle(title);
                        }
                        
                        // Create button
                        const buttonWatchStream = new ButtonBuilder()
                            .setLabel('Watch Stream')
                            .setStyle(ButtonStyle.Link)
                            .setURL(streamingUrl);
                        
                        const rowButtons = new ActionRowBuilder()
                            .addComponents(buttonWatchStream);
                        
                        // Send the message
                        await streamingChannel.send({ 
                            embeds: [embed], 
                            components: [rowButtons] 
                        });
                    } else {
                        console.error(`Streaming channel with ID ${config.WHITEMANE_STREAMERS_CHANNEL_ID} not found`);
                    }
                } catch (notificationError) {
                    console.error('Error sending stream notification:', notificationError);
                }
            }
        }
    } catch (error) {
        console.error('Error in luposOnPresenceUpdate:', error);
    }
}

async function luposOnGuildMemberRemove(client, mongo, member) {
    if (member.guild.id !== config.GUILD_ID_WHITEMANE) return;
    const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member });
    const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(member.guild);
    
    LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
    console.log(LogFormatter.memberLeftGuild(member));
    // post that they have left in the #leavers-log channel
    const leaversLogChannel = DiscordUtilityService.getChannelById(client, config.CHANNEL_ID_WHITEMANE_LEAVERS_LOG_CHANNEL_ID);
    if (leaversLogChannel) {
        let description = '';
        description += `Tag: <@${member.id}>\n`;
        description += `ID: \`${member.user.id}\`\n`;
        description += `Global Name: \`${member.user.globalName}\`\n`;
        description += `Username: \`${member.user.username}\`\n`;
        if (member.joinedTimestamp) {
            description += `Joined Server: \`${luxon.DateTime.fromMillis(member.joinedTimestamp).toLocaleString(luxon.DateTime.DATETIME_MED)}\`\n`;
        }
        description += `Current Member Count: \`${member.guild.memberCount}\`\n`;
        const embed = new EmbedBuilder()
            .setAuthor({ name: member.user.username, iconURL: member.user.displayAvatarURL() })
            .setTitle(`${member.user.username} has left the server`)
            .setDescription(description)
            .setColor('#FF0000')
            // .setTimestamp()
            // .setFooter({ text: `User ID: ${member.id}`, iconURL: member.user.displayAvatarURL() });
        await leaversLogChannel.send({ embeds: [embed] });
    }
}

async function luposOnVoiceStateUpdate(client, mongo, oldState, newState) {
    if (newState.member.guild.id !== config.GUILD_ID_WHITEMANE) return;
    if (newState.channelId) {
        console.log(LogFormatter.memberJoinedVoiceChannel(newState));
        const voiceChatterRoleId = '1392631838521954335';
        await DiscordUtilityService.addRoleToMember(client, newState.member.id, voiceChatterRoleId);
    } else {
        console.log(LogFormatter.memberLeftVoiceChannel(oldState));
    }
}

const DiscordService = {
    initializeBotLupos({ mongo, localMongo }) {
        const luposClient = DiscordWrapper.createClient('lupos', config.LUPOS_TOKEN);
        // if it's in the whitemane guild
        DiscordUtilityService.onEventClientReady(luposClient, { mongo, localMongo }, luposOnReady);
        DiscordUtilityService.onEventGuildMemberAdd(luposClient, mongo, luposOnGuildMemberAdd);
        DiscordUtilityService.onEventGuildMemberUpdate(luposClient, mongo, luposOnGuildMemberUpdate);
        DiscordUtilityService.onEventMessageCreate(luposClient, { mongo, localMongo }, luposOnMessageCreate);
        DiscordUtilityService.onEventMessageUpdate(luposClient, { mongo, localMongo }, luposOnMessageUpdate);
        DiscordUtilityService.onEventMessageReactionAdd(luposClient, mongo, luposOnReactionCreateQueue);
        DiscordUtilityService.onEventInteractionCreate(luposClient, mongo, luposOnInteractionCreate);
        DiscordUtilityService.onEventPresenceUpdate(luposClient, mongo, luposOnPresenceUpdate);
        DiscordUtilityService.onEventGuildMemberRemove(luposClient, mongo, luposOnGuildMemberRemove);
        DiscordUtilityService.onEventVoiceStateUpdate(luposClient, mongo, luposOnVoiceStateUpdate);
        updateLastMessageSentTime();

        

        // Create a collection to store your commands
        luposClient.commands = new Collection();

        // Load all commands from the commands directory
        const foldersPath = path.join(__dirname, '..', 'commands');
        const commandFolders = fs.readdirSync(foldersPath);

        for (const folder of commandFolders) {
            const commandsPath = path.join(foldersPath, folder);
            const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
            
            for (const file of commandFiles) {
                const filePath = path.join(commandsPath, file);
                const command = require(filePath);
                
                if ('data' in command && 'execute' in command) {
                    luposClient.commands.set(command.data.name, command);
                    console.log(`[INFO] Command ${command.data.name} loaded.`);
                } else {
                    console.log(`[WARNING] The command at ${filePath} is missing a required "data" or "execute" property.`);
                }
            }
        }


        // Whenever the user becomes available?
        // onEventGuildMemberAvailable(luposClient, mongo, luposOnGuildMemberAvailable);
    },
    initializeBotLuposReports(mongo) {
        const luposClient = DiscordWrapper.createClient('lupos', config.LUPOS_TOKEN);
        DiscordUtilityService.onEventClientReady(luposClient, { mongo }, luposOnReadyReports);
    },
    initializeBotBender(mongo) {
        const benderClient = DiscordWrapper.createClient('bender', config.BENDER_TOKEN);
        // DiscordUtilityService.onEventClientReady(benderClient, (client) => benderOnReady(client, mongo));
    },
    initializeBotCustomer(mongo) {
        const customerClient = DiscordWrapper.createClient('customer', config.CUSTOMER_TOKEN);
        // DiscordUtilityService.onEventClientReady(customerClient, (client) => benderOnReady(client, mongo));
    },
};

module.exports = DiscordService;
