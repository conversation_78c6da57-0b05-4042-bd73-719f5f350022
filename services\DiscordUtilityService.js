require('dotenv/config');
const luxon = require('luxon');
const { consoleLog } = require('../libraries/UtilityLibrary.js');
const config = require('../config.json');
const { Collection, ChannelType, Events } = require('discord.js');
const UtilityLibrary = require('../libraries/UtilityLibrary.js');
const PuppeteerWrapper = require('../wrappers/PuppeteerWrapper.js');
const LightWrapper = require('../wrappers/LightWrapper.js');


async function fetchMessagesWithOptionalLastId(client, channelId, maxMessages = 10, lastId = null) {
    const channel = client.channels.cache.find(channel => channel.id == channelId);
    
    if (channel) {
        let allMessages = new Collection();
        
        // Initial fetch
        let messages = await channel.messages.fetch({ limit: Math.min(100, maxMessages), before: lastId });
        allMessages = allMessages.concat(messages);
        
        // Continue fetching if we need more messages
        while (allMessages.size < maxMessages && messages.size !== 0) {
            lastId = messages.last()?.id;
            if (!lastId) break;
            
            const additionalMessagesNeeded = maxMessages - allMessages.size;
            messages = await channel.messages.fetch({ 
                limit: Math.min(100, additionalMessagesNeeded), 
                before: lastId 
            });
            
            allMessages = allMessages.concat(messages);
        }
        // If we fetched more than needed, trim the collection
        if (allMessages.size > maxMessages) {
            const trimmedCollection = new Collection();
            let count = 0;
            for (const [id, message] of allMessages) {
                if (count >= maxMessages) break;
                trimmedCollection.set(id, message);
                count++;
            }
            return trimmedCollection;
        }
        
        return allMessages;
    }
}

const DiscordUtilityService = {
    async extractAudioUrlsFromMessage(message) {
        let audioUrls = [];
        if (message?.attachments?.size) {
            for (const attachment of message.attachments.values()) {
                const isAudio = attachment.contentType.includes('audio/ogg');
                if (isAudio) {
                    audioUrls.push(attachment.url);
                }
            }
        }
        return audioUrls;
    },
    async extractImageUrlsFromMessage(message) {
        let imageUrls = [];
        // Attachments
        if (message?.attachments?.size) {
            for (const attachment of message.attachments.values()) {
                const isImage = attachment.contentType.includes('image/');
                if (isImage) {
                    imageUrls.push(attachment.url);
                }
            }
        }
        // Content
        if (message?.content) {
            // Process URLs in message content
            const urls = message.content.match(/(https?:\/\/[^\s]+)/g);
            if (urls?.length) {
                for (const url of urls) {
                    if (!url.includes('https://tenor.com/view/')) {
                        const isImage = await UtilityLibrary.isImageUrl(url);
                        if (isImage) {
                            imageUrls.push(url);
                        }
                    } else {
                        const tenorImage = await PuppeteerWrapper.scrapeTenor(url);
                        imageUrls.push(tenorImage.image);
                    }
                }
            }
        }

        return imageUrls;
    },
    // Simple
    getDisplayNameFromUserOrMember({ user, member }) {
        let displayName;
        if (user || member) {
            displayName = user?.displayName || member?.displayName;
        }
        return displayName;
    },
    getUsernameFromUser(user) {
        let username;
        if (user?.username) {
            username = user.username;
        }
        return username;
    },
    getCleanUsernameFromUser(user) {
        // Removes periods and hashes with underscores and removes any non-alphanumeric characters
        let username = DiscordUtilityService.getUsernameFromUser(user);
        if (username) {
            username = username.replace(/[.#]/g, '_').replace(/[^\w]/gi, '');
        }
        return username;
    },
    // Complex
    getCombinedNamesFromUserOrMember({ user, member }, isConsoleLog = false) {
        const { bold, faint } = UtilityLibrary.ansiEscapeCodes(isConsoleLog);
        let parts = [];
        
        if (member) {
            if (member.nickname) parts.push(bold(member.nickname));
            if (!member.nickname) parts.push(bold(member.user?.globalName));
            if (member.user?.username) parts.push(bold(member.user.username));
            if (member.user?.globalName && member.nickname) parts.push(bold(member.user.globalName));
            if (member.user?.id) parts.push(bold(`<@${member.user.id}>`));
        } else if (user) {
            parts.push(bold(user.username));
            if (user.globalName) parts.push(user.globalName);
            if (!user.globalName) {
                parts.push(`${user.tag}`);
            }
            parts.push(faint(`<@${user.id}>`));
        }
        
        return parts.join(' • ');
    },
    getCombinedGuildInformationFromGuild(guild, isConsoleLog = false) {
        const { bold, faint } = UtilityLibrary.ansiEscapeCodes(isConsoleLog);
        let combinedGuildInformation;
        if (guild) {
            combinedGuildInformation = `${bold(guild.name)} • ${faint(guild.id)}`;
        }
        return combinedGuildInformation;
    },
    getCombinedChannelInformationFromChannel(channel, isConsoleLog = false) {
        const { bold, faint } = UtilityLibrary.ansiEscapeCodes(isConsoleLog);
        let combinedChannelInformation;
        if (channel) {
            combinedChannelInformation = `#${bold(channel.name)} • ${faint(channel.id)}`;
        }
        return combinedChannelInformation;
    },
    getCombinedEmojiInformationFromReaction(reaction, isConsoleLog = false) {
        if (!reaction) return;
        const { bold, faint } = UtilityLibrary.ansiEscapeCodes(isConsoleLog);
        const emoji = reaction._emoji;
        const parts = [];
        if (emoji) {
            parts.push(bold(emoji.name));
            if (emoji.id) {
                parts.push(faint(`<:${emoji.name}:${emoji.id}>`));
            }
        }
        return parts.join(' • ');
    },
    getCombinedRoleInformationFromRole(role, isConsoleLog = false) {
        const { bold, faint } = UtilityLibrary.ansiEscapeCodes(isConsoleLog);
        let combinedRoleInformation;
        if (role) {
            combinedRoleInformation = `${bold(role.name)} • ${faint(role.id)}`;
        }
        return combinedRoleInformation;
    },
    getNameFromMessage(message) {
        let name = message?.author?.displayName || message?.author?.username || message?.user?.username;
        let username = 'default';
        if (name) {
            username = name ? name.replace(/\s+/g, '_').replace(/[^\w\s]/gi, '') : message?.author?.username || message?.user?.username;
            if (!username) {
                username = message?.author?.username || message?.user?.username || 'default';
            }
        }
        return username;
    },
    getNameFromUser(user) {
        if (user) {
            const username = user?.displayName || user?.username || user?.globalName;
            return username;
        }
    },
    getUserMentionFromMessage(message) {
        if (message) {
            // Find out why author and why user are different
            const userId = message?.author?.id || message?.user?.id;
            return `<@${userId}>`;
        }
    },
    getDiscordTagFromMessage(message) {
        if (message) {
            const userTag = message?.author?.tag || message?.user?.tag;
            return userTag;
        }
    },
    async printOutAllRoles(client) {
        // print out all roles in the order that they are in the server
        consoleLog('<');
        const roles = client.guilds.cache.get(config.GUILD_ID_WHITEMANE).roles.cache;
        const orderedRoles = roles.sort((a, b) => a.rawPosition - b.rawPosition).reverse();
        consoleLog('=', `Printing out all roles in the order that they are in the server`);
        for (const role of orderedRoles.values()) {
            console.log(`${role.name} - ${role.id}`);
        }
        consoleLog('>', 'printOutAllRoles');
    },
    async printOutAllEmojis(client) {
        consoleLog('<');
        const emojis = client.guilds.cache.get(config.GUILD_ID_WHITEMANE).emojis.cache;
        consoleLog('=', `Printing out all emojis in the server`);
        for (const emoji of emojis.values()) {
            console.log(`${emoji.name} - ${emoji.id}`);
        }
        consoleLog('>', 'printOutAllEmojis');
    },
    // MEMBER
    async getMemberFromMessage(message) {
        let member = message.guild.members.cache.get(message.author.id);
        if (!member) {
            try {
                member = await message.guild.members.fetch(message.author.id);
            } catch (error) {
                consoleLog('!', `Could not fetch member with ID ${message.author.id} in the guild ${message.guild.name}. Error: ${error.message}`);
                return null;
            }
        }
        return member;
    },
    async getMemberFromMessageAndId(message, userId) {
        if (message.guild) {
            let member = message.guild.members.cache.get(userId);
            if (!member) {
                try {
                    member = await message.guild.members.fetch(userId);
                } catch (error) {
                    consoleLog('!', `Could not fetch member with ID ${userId} in the guild ${message.guild.name}. Error: ${error.message}`);
                    return null;
                }
            }
            return member;
        }
    },
    
    // USER
    async fetchUserFromMessage(message, force = false) {
        const client = message.client;
        const userId = message.author.id;
        const user = await client.users.fetch(userId, { force: force });
        if (!user) {
            consoleLog('!', `Could not fetch user with ID ${userId}.`);
            return null;
        }
        return user;
    },
    async fetchUserFromClientAndUserId(client, userId) {
        let user = client.users.cache.get(userId);
        if (!user) {
            try {
                user = await client.users.fetch(userId);
            } catch (error) {
                consoleLog('!', `Could not fetch user with ID ${userId}. Error: ${error.message}`);
                return null;
            }
        }
        return user;
    },
    getUserByClientAndId(client, userId) {
        return client.users.cache.get(userId);
    },
    async getUserFromMessage(message, force = false) {
        const client = message.client;
        const usersId = message.author.id;
        let user = client.users.cache.get(usersId);
        if (!user) {
            try {
                user = await client.users.fetch(usersId, { force: force });
            } catch (error) {
                consoleLog('!', `Could not fetch user with ID ${usersId}. Error: ${error.message}`);
                return null;
            }
        }
        return user;
    },
    async getUserFromClientAndId(client, userId, force = false) {
        let user = client.users.cache.get(userId);
        if (!user) {
            try {
                user = await client.users.fetch(userId, { force: force });
            } catch (error) {
                consoleLog('!', `Could not fetch user with ID ${userId}. Error: ${error.message}`);
                return null;
            }
        }
        return user;
    },
    onEventClientReady(client, { mongo, localMongo }, customFunction) {
        return client.on(Events.ClientReady, async message => { customFunction(client, { mongo, localMongo }) });
    },
    onEventMessageCreate(client, { mongo, localMongo }, customFunction) {
        return client.on(Events.MessageCreate, async message => { customFunction(client, { mongo, localMongo }, message) });
    },
    onEventMessageUpdate(client, { mongo, localMongo }, customFunction) {
        return client.on(Events.MessageUpdate, async (oldMessage, newMessage) => { customFunction(client, { mongo, localMongo }, oldMessage, newMessage) });
    },
    onEventMessageReactionAdd(client, mongo, customFunction) {
        return client.on(Events.MessageReactionAdd, async (reaction, user) => { customFunction(client, mongo, reaction, user) });
    },
    onEventGuildMemberAdd(client, mongo, customFunction) {
        return client.on(Events.GuildMemberAdd, async member => { customFunction(client, mongo, member) });
    },
    onEventGuildMemberAvailable(client, mongo, customFunction) {
        return client.on(Events.GuildMemberAvailable, async member => { customFunction(client, mongo, member) });
    },
    onEventInteractionCreate(client, mongo, customFunction) {
        return client.on(Events.InteractionCreate, async interaction => { customFunction(client, mongo, interaction) });
    },
    onEventPresenceUpdate(client, mongo, customFunction) {
        return client.on(Events.PresenceUpdate, async (oldPresence, newPresence) => {
            customFunction(client, mongo, oldPresence, newPresence)
        });
    },
    onEventVoiceStateUpdate(client, mongo, customFunction) {
        return client.on(Events.VoiceStateUpdate, async (oldState, newState) => { customFunction(client, mongo, oldState, newState) });
    },
    onEventGuildMemberRemove(client, mongo, customFunction) {
        return client.on(Events.GuildMemberRemove, async member => { customFunction(client, mongo, member) });
    },
    // when user is updated
    onEventGuildMemberUpdate(client, mongo, customFunction) {
        return client.on(Events.GuildMemberUpdate, async (oldMember, newMember) => { customFunction(client, mongo, oldMember, newMember) });
    },
    async getAllServerEmojisFromMessage(message, format = 'string') {
        // format can be: array, string
        if (message.guild.emojis.cache.size) {
            const emojis = message.guild.emojis.cache.map(emoji => {
                return {
                    id: emoji.id,
                    name: emoji.name,
                    url: emoji.url
                };
            });
            if (format === 'array') {
                return emojis;
            } else if (format === 'string') {
                return emojis.map(emoji => `<${emoji.name}:${emoji.id}>`).join(', ');
            }
        } else {
            return [];
        }
    },
    // Processes multiple messages at the same time
    displayAllGuilds(client) {
        consoleLog('<')
        const guilds = getAllGuilds(client);
        consoleLog('=', `Connected Discord Servers: ${guilds.length }`)
        consoleLog('>')
    },
    // Special functions
    async fetchMessages(client, channelId, maxMessages = 10, lastId = null) {
        const channel = client.channels.cache.find(channel => channel.id == channelId);
        
        if (channel) {
            let allMessages = new Collection();
            
            // Metrics tracking
            let apiCallCount = 0;
            const startTime = Date.now();
            
            // Initial fetch - use provided lastId if available
            apiCallCount++;
            const initialFetchOptions = { limit: Math.min(100, maxMessages) };
            if (lastId) {
                initialFetchOptions.before = lastId;
            }
            
            let messages = await channel.messages.fetch(initialFetchOptions);
            allMessages = allMessages.concat(messages);
            
            // Continue fetching if we need more messages
            // console.log(`Fetched ${messages.size} messages from ${channel.name} (#${channel.id}), total: ${allMessages.size} / ${maxMessages}`);
            while (allMessages.size < maxMessages && messages.size !== 0) {
                lastId = messages.last()?.id;
                if (!lastId) break;
                
                const additionalMessagesNeeded = maxMessages - allMessages.size;
                apiCallCount++;
                messages = await channel.messages.fetch({ 
                    limit: Math.min(100, additionalMessagesNeeded), 
                    before: lastId 
                });
                
                allMessages = allMessages.concat(messages);
                // console.log(`Fetched ${messages.size} messages from ${channel.name} (#${channel.id}), total: ${allMessages.size} / ${maxMessages}`);
            }
            
            // Log metrics
            // const totalTime = Date.now() - startTime;
            // console.log(`API calls made: ${apiCallCount}`);
            // console.log(`Total time: ${totalTime}ms`);
            // console.log(`Average time per call: ${(totalTime / apiCallCount).toFixed(2)}ms`);
            // console.log(`Messages fetched: ${allMessages.size}`);
            
            // If we fetched more than needed, trim the collection
            if (allMessages.size > maxMessages) {
                const trimmedCollection = new Collection();
                let count = 0;
                for (const [id, message] of allMessages) {
                    if (count >= maxMessages) break;
                    trimmedCollection.set(id, message);
                    count++;
                }
                return trimmedCollection;
            }
            
            return allMessages;
        }
    },
    // User functions
    getBotName(client) {
        return client.user.tag;
    },
    setUserActivity(client, message) {
        return client.user.setActivity(message, { type: 4 });
    },
    // Channel functions
    getChannelById(client, channelId) {
        return client.channels.cache.get(channelId);
    },
    getChannelName(client, channelId) {
        return client.channels.cache.get(channelId)?.name;
    },
    // Guilds functions
    getGuildById(client, guildId) {
        return client.guilds.cache.get(guildId);
    },
    getAllGuilds(client) {
        let guilds = [];
        client.guilds.cache.forEach(guild => { guilds.push(guild) });
        return guilds;
    },
    getNameFromItem(item) {
        return item?.author?.displayName || item?.author?.username || item?.user?.globalName || item?.user?.username;
    },
    // REST functions
    async patchBanner(client, imageUrl) {
        return await client.rest.patch("/users/@me", { body: { banner: "data:image/gif;base64," + Buffer.from(imageUrl).toString('base64') } });
    },
    async patchBannerFromImageUrl(client, imageUrl) {
        return await client.rest.patch("/users/@me", { body: { banner: "data:image/gif;base64," + Buffer.from(await (await fetch(imageUrl)).arrayBuffer()).toString('base64') } });
    },
    async getBannerFromUserId(client, userId) {
        const getUser = await client.rest.get(`/users/${userId}`);
        return getUser.banner;
    },
    // Typing functions
    async startTypingInterval(channel) {
        let sendTypingInterval;
        const startTyping = async () => {
                await channel.sendTyping();
                sendTypingInterval = setInterval(() => {
                    channel.sendTyping().catch(error => {
                        if (sendTypingInterval) {
                            clearInterval(sendTypingInterval); 
                        }
                    });
                }, 5000);
        };
        await startTyping();
        return sendTypingInterval;
    },
    clearTypingInterval(sendTypingInterval) {
        if (sendTypingInterval) clearInterval(sendTypingInterval);
        return null;
    },
    // Message functions
    async sendMessageInChunks(sendOrReply, message, generatedTextResponse, generatedImage, imageFileName) {
        const messageChunkSizeLimit = 2000;
        let fileName = 'lupos.png';
        let imageDescription = '';

        if (imageFileName) {
            fileName = `${imageFileName.substring(0, 240)}.png`;
            imageDescription = imageFileName.substring(0, 1000);
        }

        for (let i = 0; i < generatedTextResponse.length; i += messageChunkSizeLimit) {
            const chunk = generatedTextResponse.substring(i, i + messageChunkSizeLimit);
            let messageReplyOptions = { content: chunk };
            let files = [];

            // if (generatedAudioFile && (i + messageChunkSizeLimit >= generatedTextResponse.length)) {
            //     files.push({ attachment: await fs.promises.readFile(`${BARK_VOICE_FOLDER}/${generatedAudioFile}`), name: `${generatedAudioFile}` });
            // }
            // if (generatedAudioBuffer && (i + messageChunkSizeLimit >= generatedTextResponse.length)) {
            //     files.push({ attachment: Buffer.from(generatedAudioBuffer, 'base64'), name: 'lupos.mp3' });
            // }
            if (generatedImage && (i + messageChunkSizeLimit >= generatedTextResponse.length)) {
                files.push({ attachment: Buffer.from(generatedImage, 'base64'), name: fileName, description: imageDescription });
            }
            messageReplyOptions = { ...messageReplyOptions, files: files};
            if (sendOrReply === 'send') {
                await message.channel.send(messageReplyOptions);
            } else if (sendOrReply === 'reply') {
                await message.reply(messageReplyOptions);
            }
        }
    },
    // Roles functions
    async addRoleToMember(client, userId, roleId) {
        const guild = DiscordUtilityService.getGuildById(client, config.GUILD_ID_WHITEMANE);
        const role = guild.roles.cache.find(role => role.id === roleId);
        try {
            let member = guild.members.cache.get(userId);
            const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member });
            const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(guild);
            if (!member) {
                try {
                    member = await guild.members.fetch(userId);
                } catch (error) {
                    if (error.code === 10007) {
                        console.error(`❌ [DiscordUtilityService:addRoleToMember] FAILED TO ADD ROLE, MEMBER NOT FOUND
    Member: ${combinedNames}
    Guild: ${combinedGuildInformation}`);
                        return;
                    }
                    throw error;
                }
            }
            
            if (!member.user.bot && !member.roles.cache.some(role => role.id === roleId)) {
                await member.roles.add(role);
                
                LightWrapper.cycleColor(config.PRIMARY_LIGHT_ID);
                console.log(`👤➕🏷️ [DiscordUtilityService:addRoleToMember] ADDED ROLE
    Member: ${combinedNames}
    Guild: ${combinedGuildInformation}
    Role: ${role.name}`);

            }
        } catch (error) {
            console.error(`Failed to assign role ${role.name} to user ${userId}:`, error);
        }
    },
    async removeRoleFromUser(client, userId, roleId) {
        const guild = DiscordUtilityService.getGuildById(client, config.GUILD_ID_WHITEMANE);
        const role = guild.roles.cache.find(role => role.id === roleId);
        const combinedGuildInformation = DiscordUtilityService.getCombinedGuildInformationFromGuild(guild);
        
        try {
            let member = guild.members.cache.get(userId);
            const combinedNames = DiscordUtilityService.getCombinedNamesFromUserOrMember({ member });
            if (!member) {
                try {
                    member = await guild.members.fetch(userId);
                } catch (error) {
                    if (error.code === 10007) {
                        console.error(`❌ [DiscordUtilityService:removeRoleFromUser] FAILED TO REMOVE ROLE, MEMBER NOT FOUND
    Member: ${combinedNames}
    Guild: ${combinedGuildInformation}`);
                        return;
                    }
                    throw error;
                }
            }
            
            if (!member.user.bot && member.roles.cache.some(role => role.id === roleId)) {
                await member.roles.remove(role);
                console.log(`👤➖🏷️ [DiscordUtilityService:removeRoleFromUser] REMOVED ROLE
    Member: ${combinedNames}
    Guild: ${combinedGuildInformation}
    Role: ${role.name}`);
            }
        } catch (error) {
            console.error(`❌ [DiscordUtilityService:removeRoleFromUser] FAILED TO REMOVE ROLE
    Member ID: ${userId}
    Error:`, error);
        }
    },
    // Utility functions
    async displayAllChannelActivity(client, mongo) {
        const startTime = Date.now();
        consoleLog('>', 'Displaying all channel activity (past 3 months)');
        const db = mongo.db('lupos');
        const collection = db.collection('messages');
        console.log('[START] Beginning channel activity analysis...');
        console.log(`[START] Started at: ${new Date(startTime).toISOString()}`);
        
        const guild = client.guilds.cache.find(guild => guild.name === 'Classic Whitemane');
        console.log(`[GUILD] Found guild: ${guild.name} with ${guild.channels.cache.size} total channels`);
        
        const excludedCategories = [
            'Archived',
            'Archived02',
            'Archived: First Purge',
            'Archived: SOD',
            'Archived: Alliance',
            'Archived: WoW Classes',
            '⚒ Administration',
            'Info',
            'Welcome',
            'commands',
        ];
        
        const excludedChannels = [
            '609498307626008576',
        ];
        
        console.log(`[FILTER] Excluding categories: ${excludedCategories.join(', ')}`);
        console.log(`[FILTER] Excluding ${excludedChannels.length} specific channels`);
        
        const channelStats = [];
        const globalUserStats = {}; // Track all users across all channels
        const now = luxon.DateTime.local();
        const threeMonthsAgo = now.minus({ months: 12 });
        console.log(`[TIME] Current time: ${now.toISO()}`);
        console.log(`[TIME] Three months ago: ${threeMonthsAgo.toISO()}`);
        
        // Count eligible channels
        let eligibleChannelCount = 0;
        let processedChannelCount = 0;
        let totalFetchCount = 0;
        
        for (const channel of guild.channels.cache.values()) {
            if (channel.type === ChannelType.GuildText &&
                channel.parent &&
                !excludedCategories.includes(channel.parent.name) &&
                !excludedChannels.includes(channel.id)) {
                eligibleChannelCount++;
            }
        }
        console.log(`[CHANNELS] Found ${eligibleChannelCount} eligible text channels to process`);
        console.log('----------------------------------------');
        
        // Collect channel statistics
        for (const channel of guild.channels.cache.values()) {
            if (channel.type === ChannelType.GuildText &&
                channel.parent &&
                !excludedCategories.includes(channel.parent.name) &&
                !excludedChannels.includes(channel.id)) {
                
                processedChannelCount++;
                console.log(`\n[CHANNEL ${processedChannelCount}/${eligibleChannelCount}] Processing: #${channel.name} (Category: ${channel.parent.name})`);
                
                try {
                    // Fetch messages to cover 3 months
                    let allMessages = [];
                    let lastMessageId = null;
                    let fetchMore = true;
                    let fetchCount = 0;
                    let channelFetchCount = 0;
                    let consecutiveDuplicates = 0; // Track if we're getting the same batch
                    let previousOldestId = null;
                    
                    console.log(`  [FETCH] Starting message fetch for #${channel.name}...`);
                    
                    while (fetchMore) {
                        fetchCount++;
                        channelFetchCount++;
                        totalFetchCount++;
                        
                        console.log(`  [FETCH] Fetching batch ${fetchCount}...`);
                        
                        // Fetch one batch at a time
                        const messages = await fetchMessagesWithOptionalLastId(
                            client, 
                            channel.id, 
                            100,
                            lastMessageId ? lastMessageId : undefined
                        );
                        
                        const messagesArray = Array.from(messages.values());
                        
                        if (messagesArray.length === 0) {
                            console.log(`  [FETCH] No messages found, stopping fetch`);
                            fetchMore = false;
                            break;
                        }

                        const transformUser = (user) => ({
                            // User
                            accentColor: user.accentColor,
                            avatar: user.avatar,
                            avatarDecoration: user.avatarDecoration,
                            avatarDecorationData: user.avatarDecorationData,
                            banner: user.banner,
                            bot: user.bot,
                            createdAt: user.createdAt,
                            createdTimestamp: user.createdTimestamp,
                            defaultAvatarURL: user.defaultAvatarURL,
                            discriminator: user.discriminator,
                            displayName: user.displayName,
                            dmChannel: user.dmChannel,
                            flags: user.flags,
                            globalName: user.globalName,
                            hexAccentColor: user.hexAccentColor,
                            id: user.id,
                            partial: user.partial,
                            system: user.system,
                            tag: user.tag,
                            username: user.username
                        });
                        const transformAttachments = (attachments) => {
                            // Collection<Snowflake, Attachment>
                            return attachments.map(attachment => ({
                                id: attachment.id,
                                name: attachment.name,
                                size: attachment.size,
                                url: attachment.url,
                                contentType: attachment.contentType
                            }));
                        };

                        const transformChannel = (channel) => ({
                            // If<InGuild, GuildTextBasedChannel, TextBasedChannel>
                            id: channel.id,
                            name: channel.name
                        });

                        const transformEmbeds = (embeds) => {
                            // Embed[]
                            return embeds.map(embed => ({
                                title: embed.title,
                                description: embed.description,
                                url: embed.url,
                                color: embed.color,
                                timestamp: embed.timestamp
                            }));
                        };
                        
                        const transformGuild = (guild) => {
                            // If<InGuild, Guild>
                            return {
                                id: guild.id,
                                name: guild.name
                            };
                        };

                        const transformMessageMentions = (mentions) => {
                            // MessageMentions<InGuild>
                            return {
                                users: mentions.users.map(user => transformUser(user)),
                                roles: mentions.roles.map(role => transformRole(role)),
                                // channels: mentions.channels.map(channel => transformChannel(channel))
                                everyone: mentions.everyone
                            };
                        };

                        

                        const transformMessageSnapshots = (messageSnapshots) => {
                            // Collection<Snowflake, MessageSnapshot>
                            return messageSnapshots.map(messageSnapshot => ({
                                id: messageSnapshot.id,
                                channelId: messageSnapshot.channelId,
                                author: transformUser(messageSnapshot.author),
                                content: messageSnapshot.content,
                                createdAt: messageSnapshot.createdAt,
                                editedAt: messageSnapshot.editedAt,
                                flags: messageSnapshot.flags,
                                mentions: transformMessageMentions(messageSnapshot.mentions)
                            }));
                        };

                        // Transform messages to plain objects before inserting
                        const messagesToInsert = messagesArray.map(message => ({
                            // MessageActivity | null
                            activity: message.activity,
                            // Snowflake | null
                            applicationId: message.applicationId,
                            attachments: transformAttachments(message.attachments),
                            author: transformUser(message.author),
                            // boolean
                            bulkDeletable: message.bulkDeletable,
                            // MessageCall | null
                            call: message.call,
                            channel: transformChannel(message.channel),
                            // Snowflake
                            channelId: message.channelId,
                            // string
                            cleanContent: message.cleanContent,
                            // TopLevelComponent[]
                            components: message.components,
                            // string
                            content: message.content,
                            // Date
                            createdAt: message.createdAt,
                            // number
                            createdTimestamp: message.createdTimestamp,
                            // boolean
                            crosspostable: message.crosspostable,
                            // boolean
                            deletable: message.deletable,
                            // boolean
                            editable: message.editable,
                            // Date | null
                            editedAt: message.editedAt,
                            // number | null
                            editedTimestamp: message.editedTimestamp,
                            embeds: transformEmbeds(message.embeds),
                            // Readonly<MessageFlagsBitField>
                            flags: message.flags,
                            // ClientApplication | null
                            groupActivityApplication: message.groupActivityApplication,
                            guild: transformGuild(message.guild),
                            // If<InGuild, Snowflake>
                            guildId: message.guildId,
                            // boolean
                            hasThread: message.hasThread,
                            // Snowflake
                            id: message.id,
                            // ! MISSING FROM DOCUMENTATION
                            interaction: message.interaction,
                            // MessageInteractionMetadata | null
                            interactionMetadata: message.interactionMetadata,
                            // GuildMember | null
                            member: message.member ? {
                                avatar: message.member.avatar,
                                avatarDecorationData: message.member.avatarDecorationData,
                                bannable: message.member.bannable,
                                banner: message.member.banner,
                                communicationDisabledUntil: message.member.communicationDisabledUntil,
                                communicationDisabledUntilTimestamp: message.member.communicationDisabledUntilTimestamp,
                                displayColor: message.member.displayColor,
                                displayHexColor: message.member.displayHexColor,
                                displayName: message.member.displayName,
                                flags: message.member.flags,
                                guild: {
                                    id: message.member.guild.id,
                                    name: message.member.guild.name
                                },
                                id: message.member.id,
                                joinedAt: message.member.joinedAt,
                                joinedTimestamp: message.member.joinedTimestamp,
                                kickable: message.member.kickable,
                                manageable: message.member.manageable,
                                moderatable: message.member.moderatable,
                                nickname: message.member.nickname,
                                partial: message.member.partial,
                                pending: message.member.pending,
                                permissions: message.member.permissions.toArray(),
                                premiumSince: message.member.premiumSince,
                                premiumSinceTimestamp: message.member.premiumSinceTimestamp,
                                presence: message.member.presence ? {
                                    activities: message.member.presence.activities.map(activity => ({
                                        name: activity.name,
                                        state: activity.state,
                                        type: activity.type,
                                        url: activity.url
                                    })),
                                    clientStatus: message.member.presence.clientStatus,
                                    guild: {
                                        id: message.member.presence.guild.id,
                                        name: message.member.presence.guild.name
                                    },
                                    member: {
                                        id: message.member.presence.member.id,
                                        nickname: message.member.presence.member.nickname
                                    },
                                    status: message.member.presence.status,
                                    user: {
                                        id: message.member.presence.user.id,
                                        username: message.member.presence.user.username
                                    },
                                    userId: message.member.presence.userId,
                                } : null,
                                roles: message.member.roles.cache.map(role => ({
                                    id: role.id,
                                    name: role.name
                                })),
                                user: {
                                    id: message.member.user.id,
                                    username: message.member.user.username
                                },
                                voice: message.member.voice ? {
                                    channel: {
                                        id: message.member.voice.channel.id,
                                        name: message.member.voice.channel.name
                                    },
                                    channelId: message.member.voice.channelId,
                                    deaf: message.member.voice.deaf,
                                    guild: {
                                        id: message.member.voice.guild.id,
                                        name: message.member.voice.guild.name
                                    },
                                    mute: message.member.voice.mute,
                                    requestToSpeakTimestamp: message.member.voice.requestToSpeakTimestamp,
                                    selfDeaf: message.member.voice.selfDeaf,
                                    selfMute: message.member.voice.selfMute,
                                    selfVideo: message.member.voice.selfVideo,
                                    serverDeaf: message.member.voice.serverDeaf,
                                    serverMute: message.member.voice.serverMute,
                                    sessionId: message.member.voice.sessionId,
                                    streaming: message.member.voice.streaming,
                                    suppress: message.member.voice.suppress,
                                } : null,
                            } : null,
                            mentions: transformMessageMentions(message.mentions),
                            // Collection<Snowflake, MessageSnapshot>
                            messageSnapshots: message.messageSnapshots?.map(snapshot => ({
                                id: snapshot.id,
                                content: snapshot.content,
                                createdAt: snapshot.createdAt,
                                editedAt: snapshot.editedAt,
                            })),
                            // number | string | null
                            nonce: message.nonce,
                            // false
                            partial: message.partial,
                            // boolean
                            pinnable: message.pinnable,
                            // boolean
                            pinned: message.pinned,
                            // Poll | null
                            poll: message.poll ? {
                                id: message.poll.id,
                            } : null,
                            // number | null
                            position: message.position,
                            // ReactionManager
                            reactions: message.reactions.cache.map(reaction => ({
                                // burstColors: reaction.burstColors,
                                // clientId: reaction.clientId,
                                count: reaction.count,
                                countDetails: {
                                    burst: reaction.countDetails.burst,
                                    normal: reaction.countDetails.normal
                                },
                                emoji: {
                                    animated: reaction.emoji.animated,
                                    createdAt: reaction.emoji.createdAt,
                                    createdTimestamp: reaction.emoji.createdTimestamp,
                                    id: reaction.emoji.id,
                                    identifier: reaction.emoji.identifier,
                                    name: reaction.emoji.name,
                                    // reaction: reaction.emoji.reaction, circular reference
                                    url: reaction.emoji.url(),
                                },
                                // me: reaction.me,
                                // meBurst: reaction.meBurst,
                                // message: reaction.message, circular reference
                                // partial: reaction.partial,
                                users: reaction.users.cache.map(user => ({
                                    // accentColor: user.accentColor,
                                    // avatar: user.avatar,
                                    // avatarDecoration: user.avatarDecoration,
                                    // avatarDecorationData: user.avatarDecorationData,
                                    // banner: user.banner,
                                    // bot: user.bot,
                                    // createdAt: user.createdAt,
                                    // createdTimestamp: user.createdTimestamp,
                                    // defaultAvatarURL: user.defaultAvatarURL,
                                    // discriminator: user.discriminator,
                                    displayName: user.displayName,
                                    // dmChannel: user.dmChannel,
                                    // flags: user.flags,
                                    globalName: user.globalName,
                                    // hexAccentColor: user.hexAccentColor,
                                    id: user.id,
                                    // partial: user.partial,
                                    // system: user.system,
                                    tag: user.tag,
                                    username: user.username,
                                })),
                            })),
                            // MessageReference | null
                            reference: message.reference,
                            // CommandInteractionResolvedData | null
                            resolved: message.resolved,
                            roleSubscriptionData: message.roleSubscriptionData ? {
                                id: message.roleSubscriptionData.id,
                            } : null,
                            stickers: message.stickers?.map(sticker => ({
                                id: sticker.id,
                                name: sticker.name,
                                url: sticker.url,
                            })),
                            system: message.system,
                            thread: message.thread,
                            tts: message.tts,
                            type: message.type,
                            url: message.url,
                            webhookId: message.webhookId,
                        }));

                        try {
                            await collection.insertMany(messagesToInsert);
                            console.log(`  [FETCH] Inserted ${messagesToInsert.length} messages into database`);
                        } catch (insertError) {
                            // Handle duplicate key errors gracefully (if you have unique indexes)
                            if (insertError.code === 11000) {
                                console.log(`  [FETCH] Some messages already exist in database, skipping duplicates`);
                            } else {
                                console.error(`  [FETCH] Error inserting messages:`, insertError.message);
                            }
                        }

                        // 
                        console.log(`  [FETCH] Inserted ${messagesArray.length} messages into database`);
                        
                        // Get the oldest message in this batch BEFORE adding to allMessages
                        const oldestMessage = messagesArray[messagesArray.length - 1];
                        const oldestMessageDate = luxon.DateTime.fromMillis(oldestMessage.createdTimestamp);
                        const newestMessage = messagesArray[0];
                        const newestMessageDate = luxon.DateTime.fromMillis(newestMessage.createdTimestamp);
                        
                        // Check if we're getting duplicate batches
                        if (previousOldestId === oldestMessage.id) {
                            consecutiveDuplicates++;
                            console.log(`  [FETCH] WARNING: Got same oldest message ID as previous batch (duplicate #${consecutiveDuplicates})`);
                            if (consecutiveDuplicates >= 3) {
                                console.log(`  [FETCH] ERROR: Too many duplicate batches, stopping to prevent infinite loop`);
                                fetchMore = false;
                                break;
                            }
                        } else {
                            consecutiveDuplicates = 0;
                            previousOldestId = oldestMessage.id;
                        }
                        
                        // Only add messages if they're not duplicates
                        const newMessages = messagesArray.filter(msg => 
                            !allMessages.some(existingMsg => existingMsg.id === msg.id)
                        );
                        
                        if (newMessages.length === 0) {
                            console.log(`  [FETCH] All messages in this batch are duplicates, stopping`);
                            fetchMore = false;
                            break;
                        }
                        
                        allMessages = allMessages.concat(newMessages);
                        
                        console.log(`  [FETCH] Batch ${fetchCount}: ${messagesArray.length} messages (${newMessages.length} new)`);
                        console.log(`  [FETCH] Date range: ${newestMessageDate.toFormat('yyyy-MM-dd HH:mm:ss')} to ${oldestMessageDate.toFormat('yyyy-MM-dd HH:mm:ss')}`);
                        console.log(`  [FETCH] Oldest message ID: ${oldestMessage.id}`);
                        
                        // Check if we've gone past 3 months
                        if (oldestMessageDate < threeMonthsAgo) {
                            console.log(`  [FETCH] Reached messages older than 3 months (${oldestMessageDate.toFormat('yyyy-MM-dd')} < ${threeMonthsAgo.toFormat('yyyy-MM-dd')})`);
                            fetchMore = false;
                            break;
                        }
                        
                        // Check if we've exhausted the channel
                        if (messagesArray.length < 100) {
                            console.log(`  [FETCH] Retrieved only ${messagesArray.length} messages, channel history exhausted`);
                            fetchMore = false;
                            break;
                        }
                        
                        // IMPORTANT: Update lastMessageId for the next fetch
                        lastMessageId = oldestMessage.id;
                        
                        console.log(`  [FETCH] Total unique messages collected: ${allMessages.length}`);
                        console.log(`  [FETCH] Next fetch will use before: ${lastMessageId}`);
                        
                        // Add a small delay to avoid rate limiting
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                    
                    console.log(`  [FETCH] Total fetches for this channel: ${channelFetchCount}`);
                    console.log(`  [PROCESS] Filtering messages from the last 3 months...`);
                    
                    // Filter messages from the last 3 months
                    const messagesInLastThreeMonths = allMessages.filter(
                        message => luxon.DateTime.fromMillis(message.createdTimestamp) > threeMonthsAgo
                    );
                    console.log(`  [PROCESS] Found ${messagesInLastThreeMonths.length} messages in the last 3 months (out of ${allMessages.length} total fetched)`);
                    
                    // Calculate user statistics
                    const userMessageCount = {};
                    messagesInLastThreeMonths.forEach(message => {
                        const userId = message.author.id;
                        const username = message.author.username;
                        if (!userMessageCount[userId]) {
                            userMessageCount[userId] = {
                                username: username,
                                count: 0
                            };
                        }
                        userMessageCount[userId].count++;
                        
                        // Add to global user stats
                        if (!globalUserStats[userId]) {
                            globalUserStats[userId] = {
                                username: username,
                                totalMessages: 0,
                                channels: new Set()
                            };
                        }
                        globalUserStats[userId].totalMessages++;
                        globalUserStats[userId].channels.add(channel.name);
                    });
                    
                    const uniqueUserCount = Object.keys(userMessageCount).length;
                    console.log(`  [USERS] Found ${uniqueUserCount} unique users in the last 3 months`);
                    
                    // Get top 3 users
                    const sortedUsers = Object.entries(userMessageCount)
                        .sort((a, b) => b[1].count - a[1].count)
                        .slice(0, 20)
                        .map(([userId, data]) => ({
                            username: data.username,
                            count: data.count
                        }));
                    
                    if (sortedUsers.length > 0) {
                        console.log(`  [TOP USERS] Top contributors:`);
                        sortedUsers.forEach((user, index) => {
                            console.log(`    ${index + 1}. ${user.username}: ${user.count} messages`);
                        });
                    }
                    
                    // Calculate additional metrics
                    let averageMessagesPerDay = 0;
                    let lastMessageDate = null;
                    
                    if (messagesInLastThreeMonths.length > 0) {
                        // Calculate days span for accurate average
                        const oldestRecentMessage = messagesInLastThreeMonths[messagesInLastThreeMonths.length - 1];
                        const newestMessage = messagesInLastThreeMonths[0];
                        const oldestDate = luxon.DateTime.fromMillis(oldestRecentMessage.createdTimestamp);
                        const newestDate = luxon.DateTime.fromMillis(newestMessage.createdTimestamp);
                        const daySpan = Math.max(1, newestDate.diff(oldestDate, 'days').days);
                        
                        averageMessagesPerDay = messagesInLastThreeMonths.length / daySpan;
                        lastMessageDate = newestDate;
                        
                        console.log(`  [METRICS] Message span: ${daySpan.toFixed(1)} days`);
                        console.log(`  [METRICS] Average messages/day: ${averageMessagesPerDay.toFixed(2)}`);
                        console.log(`  [METRICS] Last message: ${lastMessageDate.toFormat('yyyy-MM-dd HH:mm')}`);
                    } else {
                        console.log(`  [METRICS] No messages in the last 3 months`);
                    }
                    
                    channelStats.push({
                        channel: channel,
                        messageCount: messagesInLastThreeMonths.length,
                        uniqueUsers: uniqueUserCount,
                        topUsers: sortedUsers,
                        averageMessagesPerDay: averageMessagesPerDay,
                        lastMessageDate: lastMessageDate,
                        categoryName: channel.parent ? channel.parent.name : 'No Category'
                    });
                    
                    console.log(`  [COMPLETE] Successfully processed #${channel.name}`);
                    
                } catch (error) {
                    console.error(`  [ERROR] Failed to fetch messages for channel ${channel.name}:`, error.message);
                    console.error(`  [ERROR] Stack trace:`, error.stack);
                }
            }
        }
        
        console.log('\n----------------------------------------');
        console.log('[SORT] Sorting channels by average messages per day...');
        // Sort channels by average messages per day (descending)
        channelStats.sort((a, b) => b.averageMessagesPerDay - a.averageMessagesPerDay);
        console.log('[SORT] Sorting complete (by average messages/day)');
        
        // Display results
        console.log('\n=== Channel Activity Report (Past 3 Months) ===');
        console.log('=== Sorted by Average Messages Per Day ===\n');
        console.log('Rank | Avg/Day | Messages | Users | Days Ago | Category            | Channel Name         | Top 3 Users');
        console.log('-----|---------|----------|-------|----------|---------------------|----------------------|-------------');
        
        channelStats.forEach((stat, index) => {
            const rank = (index + 1).toString().padStart(4, ' ');
            const avgPerDay = stat.averageMessagesPerDay.toFixed(2).padStart(7, ' ');
            const messageCount = stat.messageCount.toString().padStart(8, ' ');
            const uniqueUsers = stat.uniqueUsers.toString().padStart(5, ' ');
            
            let daysSinceLastMessage = 'N/A';
            if (stat.lastMessageDate) {
                const daysDiff = now.diff(stat.lastMessageDate, 'days').days;
                daysSinceLastMessage = daysDiff.toFixed(0).padStart(8, ' ');
            } else {
                daysSinceLastMessage = daysSinceLastMessage.padStart(8, ' ');
            }
            
            const category = stat.categoryName.substring(0, 20).padEnd(20, ' ');
            const channelName = stat.channel.name.substring(0, 20).padEnd(20, ' ');
            
            // Format top 3 users
            let topUsersStr = '';
            if (stat.topUsers.length > 0) {
                topUsersStr = stat.topUsers
                    .map((user, idx) => `${idx + 1}. ${user.username} (${user.count})`)
                    .join(', ');
            } else {
                topUsersStr = 'No activity';
            }
            
            console.log(`${rank} | ${avgPerDay} | ${messageCount} | ${uniqueUsers} | ${daysSinceLastMessage} | ${category} | ${channelName} | ${topUsersStr}`);
        });
        
        // Summary statistics
        const totalMessages = channelStats.reduce((sum, stat) => sum + stat.messageCount, 0);
        const activeChannels = channelStats.filter(stat => stat.messageCount > 0).length;
        const inactiveChannels = channelStats.filter(stat => stat.messageCount === 0).length;
        const totalUniqueUsers = Object.keys(globalUserStats).length;
        
        // Find the most active channel by average messages per day
        const mostActiveByAverage = channelStats[0];
        
        // Get top 10 users across all channels
        const topTenUsers = Object.entries(globalUserStats)
            .sort((a, b) => b[1].totalMessages - a[1].totalMessages)
            .slice(0, 10)
            .map(([userId, data]) => ({
                username: data.username,
                totalMessages: data.totalMessages,
                channelCount: data.channels.size
            }));
        
        const endTime = Date.now();
        const totalTimeSeconds = ((endTime - startTime) / 1000).toFixed(2);
        const totalTimeMinutes = (totalTimeSeconds / 60).toFixed(2);
        
        console.log('\n=== Summary ===');
        console.log(`[SUMMARY] Total messages (3 months): ${totalMessages}`);
        console.log(`[SUMMARY] Active channels: ${activeChannels}`);
        console.log(`[SUMMARY] Inactive channels: ${inactiveChannels}`);
        console.log(`[SUMMARY] Most active channel (by avg/day): ${mostActiveByAverage?.channel.name || 'N/A'} (${mostActiveByAverage?.averageMessagesPerDay.toFixed(2) || 0} messages/day)`);
        console.log(`[SUMMARY] Total channels processed: ${processedChannelCount}`);
        console.log(`[SUMMARY] Total API fetches made: ${totalFetchCount}`);
        console.log(`[SUMMARY] Average fetches per channel: ${(totalFetchCount / processedChannelCount).toFixed(2)}`);
        console.log(`[SUMMARY] Total unique users across all channels: ${totalUniqueUsers}`);
        console.log(`[SUMMARY] Total execution time: ${totalTimeSeconds} seconds (${totalTimeMinutes} minutes)`);
        console.log(`[SUMMARY] Completed at: ${new Date(endTime).toISOString()}`);
        
        // Display top 10 users
        console.log('\n=== Top 10 Most Active Users (Past 3 Months) ===');
        console.log('Rank | Username                | Total Messages | Active Channels');
        console.log('-----|-------------------------|----------------|----------------');
        
        topTenUsers.forEach((user, index) => {
            const rank = (index + 1).toString().padStart(4, ' ');
            const username = user.username.substring(0, 23).padEnd(23, ' ');
            const totalMessages = user.totalMessages.toString().padStart(14, ' ');
            const channelCount = user.channelCount.toString().padStart(15, ' ');
            
            console.log(`${rank} | ${username} | ${totalMessages} | ${channelCount}`);
        });
        
        console.log('\n[END] Channel activity analysis complete!');
        consoleLog('>');
    },
    async calculateMessagesSentOnAveragePerDayInChannel(client, channelId) {
        console.log(`Calculating average messages sent in channel ${channelId} over the date range in the messages...`);
        const channel = client.channels.cache.get(channelId);
        if (!channel) {
            console.log(`Channel with ID ${channelId} not found or is not a text channel.`);
            return;
        }

        const now = Date.now();
        
        let messageCount = 0;
        let lastMessageDate = null;

        try {
            let recentMessages = (await DiscordUtilityService.fetchMessages(client, channelId, 123)).reverse()
            for (const message of recentMessages.values()) {
                messageCount++;
                if (!lastMessageDate || message.createdTimestamp > lastMessageDate.getTime()) {
                    lastMessageDate = new Date(message.createdTimestamp);
                }
            }
        } catch (error) {
            console.log(`Error fetching messages from channel ${channel.name}: ${error.message}`);
            return;
        }
        
        const daysSinceStart = Math.max(1, Math.ceil((now - lastMessageDate.getTime()) / (24 * 60 * 60 * 1000)));
        const averageMessagesPerHour = (messageCount / (daysSinceStart * 24)).toFixed(2);

        console.log(`Channel: ${channel.name}`);
        console.log(`Messages sent in the last ${daysSinceStart} days: ${messageCount}`);
        console.log(`Average messages sent per hour: ${averageMessagesPerHour}`);
        if (lastMessageDate) {
            console.log(`Last message date: ${lastMessageDate.toISOString()}`);
        } else {
            console.log('No messages found in the specified period.');
        }
    }
};

module.exports = DiscordUtilityService;
