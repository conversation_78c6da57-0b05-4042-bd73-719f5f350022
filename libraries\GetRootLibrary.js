const path = require('path');
const fs = require('fs');

function getRootDir() {
    let currentDir = __dirname;
    
    while (true) {
        if (fs.existsSync(path.join(currentDir, 'package.json'))) {
            return currentDir;
        }
        
        const parentDir = path.resolve(currentDir, '..');
        if (parentDir === currentDir) {
            throw new Error('Root directory not found!');
        }
        currentDir = parentDir;
    }
}

module.exports = getRootDir();
