# Statistics
- How many unique members per channel
- Top 30 authors per channel


- Top 10 most active members in the server
- Top 10 most reactive members in the server
- Top 5 most active channels
- Top 3 most active members per channel

# Unique members in the politics channel
```js
db.getCollection('messages').aggregate(
  [
    { $match: { 'channel.name': 'politics' } },
    { $group: { _id: '$author.displayName' } },
    { $sort: { _id: 1 } }
  ],
  { maxTimeMS: 60000, allowDiskUse: true }
);
```

# Get top 30 authors that attach files:
```js
db.getCollection('messages').aggregate(
  [
    {
      $match: {
        'channel.name': 'politics',
        'author.displayName': { $ne: 'Lupos' },
        attachments: { $exists: true, $ne: [] }
      }
    },
    { $unwind: '$attachments' },
    {
      $group: {
        _id: '$author.displayName',
        attachmentCount: { $sum: 1 }
      }
    },
    { $sort: { attachmentCount: -1 } },
    { $limit: 30 }
  ],
  { maxTimeMS: 60000, allowDiskUse: true }
);
```

# Get the top 30 authors in the politics channel
```js
db.getCollection('messages').aggregate(
  [
    { $match: { 'channel.name': 'politics' } },
    { $sortByCount: '$author.displayName' },
    { $limit: 30 }
  ],
  { maxTimeMS: 60000, allowDiskUse: true }
);
```
# Top 30 authors who received the most reactions to their messages in the politics channel
```js
db.getCollection('messages').aggregate(
  [
    { $match: { 'channel.name': 'politics' } },
    { $unwind: '$reactions' },
    {
      $group: {
        _id: '$author.displayName',
        totalReactions: {
          $sum: '$reactions.count'
        }
      }
    },
    { $sort: { totalReactions: -1 } },
    { $limit: 30 }
  ],
  { maxTimeMS: 60000, allowDiskUse: true }
);
```
# Top 30 authors that have reacted to messages the most in the politics channel